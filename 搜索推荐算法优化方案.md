# 搜索推荐算法优化方案

## 问题分析

### 当前算法存在的问题

1. **过度复杂化**
   - 代码中包含大量复杂的NLP处理和权重计算
   - 算法逻辑冗余，维护困难
   - 性能开销大，响应速度慢

2. **推荐质量差**
   - 生成的推荐词语义不通畅
   - 缺乏商业价值，不符合用户搜索习惯
   - 推荐词汇过于技术化，用户体验差

3. **个性化不足**
   - 没有充分利用用户搜索历史
   - 缺乏基于用户行为的智能推荐
   - 推荐结果千篇一律，缺乏针对性

## 优化方案

### 核心设计理念

模仿淘宝推荐逻辑，采用**简洁高效**的算法，重点关注：
- 用户搜索习惯分析
- 商品词汇质量评估
- 个性化推荐生成
- 实时热度计算

### 算法架构

```
搜索推荐算法
├── 个性化推荐 (已登录用户)
│   ├── 用户搜索历史分析
│   ├── 兴趣类别识别
│   └── 相关词汇生成
├── 热门推荐 (所有用户)
│   ├── 全站搜索热度统计
│   ├── 时间衰减权重计算
│   └── 商品质量权重
└── 智能推荐 (基于商品标签)
    ├── 高价值词汇筛选
    ├── 词汇权重计算
    └── 语义完整性保证
```

### 数据利用策略

1. **ls_word表**
   - 利用`tags`字段进行词汇质量评估
   - 优先推荐高权重标签词汇（产品-品牌、产品类型-简单等）
   - 过滤低价值和负价值标签词汇

2. **ls_search_record表**
   - 分析用户搜索历史和频次
   - 计算搜索热度和时间衰减
   - 生成个性化推荐

3. **ls_goods表**
   - 利用`split_word`字段获取商品关键词
   - 结合销量数据计算商品热度权重
   - 生成语义完整的推荐词汇

## 实现方案

### 1. 重写findlists方法

```php
public static function findlists($userId)
{
    try {
        $recommendations = [];
        
        // 个性化推荐
        if ($userId) {
            $personalWords = self::getPersonalizedRecommendations($userId);
            $recommendations = array_merge($recommendations, $personalWords);
        }
        
        // 热门推荐
        $hotWords = self::getHotRecommendations();
        $recommendations = array_merge($recommendations, $hotWords);
        
        // 智能推荐
        $smartWords = self::getSmartRecommendations($userId);
        $recommendations = array_merge($recommendations, $smartWords);
        
        // 处理和过滤
        $finalRecommendations = self::processRecommendations($recommendations);
        
        return [
            'find_lists' => array_slice($finalRecommendations, 0, 12)
        ];
        
    } catch (\Exception $e) {
        return self::getFallbackRecommendations();
    }
}
```

### 2. 个性化推荐算法

```php
private static function getPersonalizedRecommendations($userId)
{
    // 1. 获取用户搜索历史（最近30天）
    $userHistory = 获取用户搜索记录();
    
    // 2. 分析用户兴趣类别
    $interests = self::analyzeUserInterests($userHistory);
    
    // 3. 生成相关推荐词汇
    $recommendations = [];
    foreach ($interests as $interest) {
        $relatedWords = self::getRelatedWordsByInterest($interest);
        $recommendations = array_merge($recommendations, $relatedWords);
    }
    
    return $recommendations;
}
```

### 3. 热门推荐算法

```php
private static function getHotRecommendations()
{
    // 1. 获取最近7天热门搜索词
    $hotWords = 获取热门搜索词();
    
    // 2. 计算热度分数（时间衰减 + 搜索频次）
    foreach ($hotWords as $word) {
        $timeDecay = self::calculateTimeDecay($word['last_search']);
        $hotScore = $word['total_count'] * $timeDecay;
        
        if ($hotScore >= 阈值) {
            $recommendations[] = $word['keyword'];
        }
    }
    
    return $recommendations;
}
```

### 4. 智能推荐算法

```php
private static function getSmartRecommendations($userId)
{
    // 1. 从ls_word表获取高价值词汇
    $highValueWords = 获取高权重标签词汇();
    
    // 2. 计算词汇权重并筛选
    foreach ($highValueWords as $wordInfo) {
        $weight = self::calculateWordWeight($wordInfo['tags']);
        if ($weight >= 15) {
            $recommendations[] = $wordInfo['word'];
        }
    }
    
    // 3. 获取热门商品关键词
    $hotGoodsWords = 获取热门商品词汇();
    
    return array_merge($recommendations, $hotGoodsWords);
}
```

## 词汇权重系统

基于ls_word表的tags字段，建立词汇质量评估体系：

| 标签类型 | 权重分数 | 说明 |
|---------|---------|------|
| 产品-品牌 | 30 | 最高价值，如"苹果"、"华为" |
| 产品类型-简单 | 25 | 核心商品词，如"手机"、"电脑" |
| 产品类型属性词 | 22 | 商品属性，如"智能"、"无线" |
| 促销词 | 20 | 营销词汇，如"特价"、"促销" |
| 基本词-中文 | 5 | 基础词汇，权重较低 |
| 色情词汇-中文 | -10 | 负面词汇，需要过滤 |
| 仿品词类 | -10 | 违规词汇，需要过滤 |

## 优化效果

### 推荐质量提升

1. **语义完整性**
   - 生成"苹果手机"而不是单独的"苹果"
   - 推荐"运动鞋"而不是"运动"+"鞋"

2. **商业价值**
   - 优先推荐有实际商品的关键词
   - 过滤无意义和违规词汇

3. **个性化程度**
   - 基于用户搜索历史的精准推荐
   - 不同用户看到不同的推荐结果

### 性能优化

1. **算法简化**
   - 移除复杂的NLP处理逻辑
   - 减少不必要的计算开销

2. **缓存机制**
   - 个人推荐缓存5分钟
   - 全局热搜缓存10分钟

3. **数据库优化**
   - 合理利用索引
   - 减少复杂查询

## 部署建议

### 1. 渐进式部署

- 先在测试环境验证算法效果
- 小流量灰度测试
- 逐步全量上线

### 2. 监控指标

- 推荐词点击率
- 用户搜索转化率
- 算法响应时间
- 推荐词质量评分

### 3. 持续优化

- 定期分析用户反馈
- 调整词汇权重配置
- 优化推荐算法参数

## 总结

新的搜索推荐算法具有以下优势：

1. **简洁高效** - 算法逻辑清晰，易于维护
2. **个性化强** - 基于用户行为的精准推荐
3. **质量可控** - 基于词汇标签的质量评估体系
4. **实时性好** - 快速响应用户需求变化
5. **商业价值高** - 推荐词汇具有实际商业意义

通过这次优化，搜索推荐功能将更好地服务用户，提升整体的搜索体验和商业转化效果。