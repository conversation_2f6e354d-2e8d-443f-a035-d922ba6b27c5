# 店铺兴趣排序功能说明

## 功能概述

基于用户兴趣的店铺列表排序功能，通过分析用户的历史行为数据，为用户提供个性化的店铺推荐排序。

## 实现原理

### 1. 用户兴趣数据收集

系统会收集以下用户行为数据来分析兴趣：

- **浏览记录**：从 `goods_footprint` 表获取用户浏览的商品，分析分类和店铺偏好
- **搜索记录**：从 `search_record` 表获取用户搜索关键词，了解用户需求
- **收藏记录**：从 `goods_collect` 表获取用户收藏的商品，分析店铺偏好
- **购买记录**：从 `order` 和 `order_goods` 表获取用户购买历史，权重最高

### 2. 兴趣权重计算

不同行为的权重设置：
- 浏览记录：基础权重 1-3（三级分类权重最高）
- 收藏记录：权重 x2
- 购买记录：权重 x5（最高优先级）

### 3. 排序算法

生成的排序规则按优先级：
1. 用户感兴趣的店铺（基于历史行为）
2. 用户感兴趣的分类对应的店铺
3. 权威验厂等级（yan_level）
4. 店铺权重（weight）
5. 店铺评分（score）
6. 店铺ID（id）

## 接口变更

### `/api/shop/getShopList`

**变更内容**：
- 自动获取当前登录用户的 `user_id`
- 如果用户已登录，使用基于兴趣的排序
- 如果用户未登录，使用默认排序

**兼容性**：
- 完全向后兼容，不影响现有功能
- 未登录用户体验不变
- 已登录用户获得个性化排序

## 缓存机制

- 用户兴趣数据缓存：30分钟
- 排序规则缓存：30分钟
- 缓存键格式：`shop_interest_order_{user_id}`

## 性能优化

1. **缓存策略**：避免重复计算用户兴趣
2. **数据限制**：限制查询的历史数据量（最近30天）
3. **异常处理**：兴趣分析失败时自动降级到默认排序
4. **SQL优化**：使用 CASE WHEN 语句进行高效排序

## 数据表依赖

- `goods_footprint`：商品浏览足迹
- `search_record`：搜索记录
- `goods_collect`：商品收藏
- `order`：订单表
- `order_goods`：订单商品表
- `goods`：商品表
- `shop`：店铺表

## 使用示例

```php
// 用户已登录的情况下调用接口
GET /api/shop/getShopList?page_no=1&page_size=10

// 系统会自动：
// 1. 获取用户ID
// 2. 分析用户兴趣
// 3. 生成个性化排序
// 4. 返回排序后的店铺列表
```

## 监控和日志

- 兴趣分析失败会记录错误日志
- 可通过日志监控功能使用情况
- 支持通过缓存命中率监控性能

## 后续优化方向

1. 增加更多行为数据源（点击、分享等）
2. 优化权重算法
3. 增加实时兴趣更新
4. 支持兴趣衰减机制
