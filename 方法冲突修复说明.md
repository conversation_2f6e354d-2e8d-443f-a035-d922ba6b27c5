# 方法冲突修复说明

## 问题描述

在 `app/api/logic/SearchRecordLogic.php` 文件中出现了方法重复定义的错误：

```
Cannot redeclare app\api\logic\SearchRecordLogic::calculateWordWeight()
```

## 问题原因

文件中存在两个同名但签名不同的 `calculateWordWeight` 方法：

1. **静态方法**（第483行）：
   ```php
   private static function calculateWordWeight($tags)
   ```
   - 参数：字符串类型的标签
   - 用途：基于标签字符串计算权重

2. **实例方法**（第4337行）：
   ```php
   protected function calculateWordWeight(array $word): int
   ```
   - 参数：数组类型的词汇对象
   - 用途：基于词汇数组计算权重

虽然参数类型不同，但PHP不允许同一个类中有相同名称的方法。

## 修复方案

### 1. 重命名实例方法

将实例方法重命名为 `calculateWordWeightFromArray`：

```php
// 修复前
protected function calculateWordWeight(array $word): int

// 修复后  
protected function calculateWordWeightFromArray(array $word): int
```

### 2. 更新方法调用

找到所有调用实例方法的地方并更新：

**位置1**（第4302-4303行）：
```php
// 修复前
$aWeight = $this->calculateWordWeight($a);
$bWeight = $this->calculateWordWeight($b);

// 修复后
$aWeight = $this->calculateWordWeightFromArray($a);
$bWeight = $this->calculateWordWeightFromArray($b);
```

**位置2**（第4361行）：
```php
// 修复前
'weight' => $this->calculateWordWeight($baseWord) + $this->calculateWordWeight($optionalWord),

// 修复后
'weight' => $this->calculateWordWeightFromArray($baseWord) + $this->calculateWordWeightFromArray($optionalWord),
```

## 修复结果

### ✅ 解决的问题

1. **消除方法冲突**：两个方法现在有不同的名称，不再冲突
2. **保持功能完整**：所有原有功能都正常工作
3. **代码清晰度**：方法名更明确地表达了参数类型

### ✅ 验证测试

- 静态方法 `calculateWordWeight()` 正常工作
- 实例方法 `calculateWordWeightFromArray()` 正常工作
- 权重计算结果正确

## 方法说明

### 静态方法：`calculateWordWeight($tags)`

- **用途**：基于标签字符串计算权重
- **参数**：`$tags` - 逗号分隔的标签字符串
- **返回**：整数权重值
- **调用方式**：`self::calculateWordWeight($tags)`

### 实例方法：`calculateWordWeightFromArray(array $word)`

- **用途**：基于词汇数组计算权重  
- **参数**：`$word` - 包含tags字段的词汇数组
- **返回**：整数权重值
- **调用方式**：`$this->calculateWordWeightFromArray($word)`

## 最佳实践建议

1. **避免方法名冲突**：即使参数不同，也应避免同名方法
2. **明确方法命名**：方法名应清楚表达参数类型和用途
3. **代码审查**：添加新方法时检查是否与现有方法冲突
4. **单元测试**：为关键方法编写测试确保修改后功能正常

## 总结

通过重命名实例方法并更新相关调用，成功解决了方法冲突问题。修复后的代码更加清晰，功能完整，避免了PHP的方法重复定义错误。