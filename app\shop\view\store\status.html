{layout name="layout1" /}
<style>
    :root {
        --primary-color: #667eea;
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --info-color: #3b82f6;
        --bg-primary: #ffffff;
        --bg-secondary: #f8fafc;
        --bg-tertiary: #f1f5f9;
        --text-primary: #1e293b;
        --text-secondary: #64748b;
        --text-muted: #94a3b8;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --radius-sm: 6px;
        --radius-md: 12px;
        --radius-lg: 16px;
    }

    body {
        background: var(--bg-secondary);
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .status-container {
        padding: 24px;
        max-width: 800px;
        margin: 0 auto;
    }

    .status-card {
        background: var(--bg-primary);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
        overflow: hidden;
    }

    .status-header {
        background: var(--primary-gradient);
        color: white;
        padding: 24px;
        text-align: center;
    }

    .status-title {
        font-size: 24px;
        font-weight: 700;
        margin: 0 0 8px 0;
    }

    .status-subtitle {
        font-size: 14px;
        opacity: 0.9;
        margin: 0;
    }

    .status-content {
        padding: 32px;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 24px;
        margin-bottom: 32px;
    }

    .info-item {
        background: var(--bg-secondary);
        border-radius: var(--radius-md);
        padding: 20px;
        border: 1px solid var(--border-color);
    }

    .info-label {
        font-size: 14px;
        font-weight: 600;
        color: var(--text-secondary);
        margin-bottom: 8px;
    }

    .info-value {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 8px 16px;
        border-radius: 50px;
        font-size: 14px;
        font-weight: 600;
        margin: 8px 4px;
    }

    .status-badge.success {
        background: rgba(16, 185, 129, 0.1);
        color: var(--success-color);
        border: 1px solid rgba(16, 185, 129, 0.2);
    }

    .status-badge.warning {
        background: rgba(245, 158, 11, 0.1);
        color: var(--warning-color);
        border: 1px solid rgba(245, 158, 11, 0.2);
    }

    .status-badge.info {
        background: rgba(59, 130, 246, 0.1);
        color: var(--info-color);
        border: 1px solid rgba(59, 130, 246, 0.2);
    }

    .status-badge.danger {
        background: rgba(239, 68, 68, 0.1);
        color: var(--danger-color);
        border: 1px solid rgba(239, 68, 68, 0.2);
    }

    .timeline {
        position: relative;
        padding-left: 32px;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 16px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: var(--border-color);
    }

    .timeline-item {
        position: relative;
        margin-bottom: 24px;
        background: var(--bg-primary);
        border-radius: var(--radius-md);
        padding: 20px;
        border: 1px solid var(--border-color);
    }

    .timeline-item::before {
        content: '';
        position: absolute;
        left: -24px;
        top: 24px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: var(--primary-color);
        border: 3px solid var(--bg-primary);
    }

    .timeline-item.completed::before {
        background: var(--success-color);
    }

    .timeline-item.current::before {
        background: var(--warning-color);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.2); opacity: 0.7; }
        100% { transform: scale(1); opacity: 1; }
    }

    .timeline-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 8px;
    }

    .timeline-desc {
        font-size: 14px;
        color: var(--text-secondary);
        line-height: 1.6;
    }

    .timeline-time {
        font-size: 12px;
        color: var(--text-muted);
        margin-top: 8px;
    }

    .docs-section {
        margin-top: 32px;
        padding-top: 32px;
        border-top: 1px solid var(--border-color);
    }

    .docs-title {
        font-size: 18px;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 16px;
    }

    .docs-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 16px;
    }

    .doc-item {
        background: var(--bg-secondary);
        border-radius: var(--radius-md);
        padding: 16px;
        border: 1px solid var(--border-color);
        text-align: center;
        transition: all 0.3s ease;
    }

    .doc-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .doc-icon {
        font-size: 32px;
        margin-bottom: 8px;
    }

    .doc-name {
        font-size: 14px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 4px;
    }

    .doc-link {
        font-size: 12px;
        color: var(--primary-color);
        text-decoration: none;
    }

    .doc-link:hover {
        text-decoration: underline;
    }
</style>

<div class="status-container">
    <div class="status-card">
        <!-- 状态头部 -->
        <div class="status-header">
            <h1 class="status-title">📋 入驻申请状态</h1>
            <p class="status-subtitle">查看您的集采联盟入驻申请进度</p>
        </div>

        <!-- 状态内容 -->
        <div class="status-content">
            <!-- 基本信息 -->
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">申请编号</div>
                    <div class="info-value">{$detail.order_sn}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">保证金金额</div>
                    <div class="info-value">¥{$detail.deposit_amount}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">支付状态</div>
                    <div class="info-value">
                        {if $detail.pay_status == 1}
                            <span class="status-badge success">✅ 已支付</span>
                        {else}
                            <span class="status-badge warning">⏳ 待支付</span>
                        {/if}
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-label">审核状态</div>
                    <div class="info-value">
                        {if $detail.status == 0}
                            <span class="status-badge info">⏳ 待审核</span>
                        {elseif $detail.status == 1}
                            <span class="status-badge success">✅ 审核通过</span>
                        {elseif $detail.status == 2}
                            <span class="status-badge danger">❌ 审核不通过</span>
                        {else}
                            <span class="status-badge warning">❓ 状态异常</span>
                        {/if}
                    </div>
                </div>
            </div>

            <!-- 进度时间线 -->
            <div class="timeline">
                <div class="timeline-item completed">
                    <div class="timeline-title">📝 提交申请</div>
                    <div class="timeline-desc">您已成功提交入驻申请并上传相关文档</div>
                    <div class="timeline-time">{$detail.create_time|date='Y-m-d H:i:s'}</div>
                </div>

                {if $detail.pay_status == 1}
                <div class="timeline-item completed">
                    <div class="timeline-title">💰 支付保证金</div>
                    <div class="timeline-desc">保证金支付成功，支付方式：{$detail.payment_method}</div>
                    <div class="timeline-time">{$detail.payment_date|date='Y-m-d H:i:s'}</div>
                </div>
                {else}
                <div class="timeline-item current">
                    <div class="timeline-title">💰 支付保证金</div>
                    <div class="timeline-desc">请完成保证金支付以继续审核流程</div>
                    <div class="timeline-time">待完成</div>
                </div>
                {/if}

                {if $detail.status == 1}
                <div class="timeline-item completed">
                    <div class="timeline-title">✅ 审核通过</div>
                    <div class="timeline-desc">恭喜！您的入驻申请已通过审核，欢迎加入集采联盟</div>
                    <div class="timeline-time">{$detail.updated_at}</div>
                </div>
                {elseif $detail.status == 2}
                <div class="timeline-item danger">
                    <div class="timeline-title">❌ 审核不通过</div>
                    <div class="timeline-desc">很抱歉，您的申请未通过审核。原因：{$detail.remark|default='暂无说明'}</div>
                    <div class="timeline-time">{$detail.updated_at}</div>
                    <!-- 重新上传区域 -->
                    <div class="reupload-section" style="margin-top: 20px; padding: 20px; background: var(--bg-secondary); border-radius: var(--radius-md); border: 2px dashed var(--danger-color);">
                        <div class="reupload-header" style="display: flex; align-items: center; gap: 12px; margin-bottom: 16px;">
                            <div style="width: 32px; height: 32px; background: var(--danger-color); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 16px;">📤</div>
                            <div style="font-size: 16px; font-weight: 600; color: var(--text-primary);">重新上传协议文档</div>
                        </div>
                        <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 16px;">
                            请重新下载协议文档，签字盖章后重新上传。上传成功后状态将变为待审核。
                            <br>
                            <a href="{$domain}/uploads/documents/20250807121123f62b78925.docx" target="_blank" style="color: var(--primary-color); text-decoration: none; font-weight: 600;" download="集采联盟入驻协议.doc">
                                <i class="layui-icon layui-icon-download-circle"></i>
                                点击下载协议文档
                            </a>
                        </div>
                        <div class="reupload-area" style="border: 2px dashed var(--border-color); border-radius: var(--radius-md); padding: 24px; text-align: center; background: var(--bg-primary); cursor: pointer; transition: all 0.3s ease;" id="reuploadArea">
                            <div style="font-size: 32px; color: var(--text-muted); margin-bottom: 12px;">📁</div>
                            <div style="font-size: 16px; font-weight: 600; color: var(--text-primary); margin-bottom: 8px;">点击选择文件或拖拽到此处</div>
                            <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 16px;">支持 jpg、png、jpeg 格式，最大 10MB</div>
                            <button type="button" class="layui-btn layui-btn-normal" id="reuploadBtn">
                                <i class="layui-icon layui-icon-upload"></i>
                                选择文件
                            </button>
                        </div>
                        <div class="reupload-file-list" id="reuploadFileList" style="margin-top: 16px; display: none;">
                            <!-- 文件列表将在这里动态生成 -->
                        </div>
                        <div style="margin-top: 16px; text-align: center;">
                            <button type="button" class="layui-btn layui-btn-normal" id="submitReuploadBtn" style="display: none;">
                                <i class="layui-icon layui-icon-ok"></i>
                                提交重新上传
                            </button>
                        </div>
                    </div>
                </div>
                {elseif $detail.pay_status == 1}
                <div class="timeline-item current">
                    <div class="timeline-title">🔍 资料审核</div>
                    <div class="timeline-desc">我们正在审核您提交的申请资料，请耐心等待</div>
                    <div class="timeline-time">审核中...</div>
                </div>
                {/if}
            </div>

            <!-- 上传文档 -->
            {if !empty($detail.docs)}
            <div class="docs-section">
                <h3 class="docs-title">📎 已上传文档</h3>
                <div class="docs-list">
                    {foreach $detail.docs_array as $doc}
                    <div class="doc-item">
                        <div class="doc-icon">📄</div>
                        <div class="doc-name">协议文档</div>
                        <a href="{$doc}" target="_blank" class="doc-link">查看文档</a>
                    </div>
                    {/foreach}
                </div>
            </div>
            {/if}
        </div>
    </div>
</div>

<script>
layui.use(['layer', 'upload'], function () {
    var layer = layui.layer;
    var upload = layui.upload;

    // 页面加载动画
    $('.status-card').hide().fadeIn(800);

    // 时间线项目动画
    $('.timeline-item').each(function(index) {
        $(this).delay(index * 200).animate({opacity: 1}, 500);
    });

    // 重新上传功能相关变量
    var reuploadedFiles = [];
    var reuploadFileIndex = 0;

    // 重新上传区域悬停效果
    $('#reuploadArea').hover(
        function() {
            $(this).css({
                'border-color': 'var(--primary-color)',
                'background': 'rgba(102, 126, 234, 0.02)'
            });
        },
        function() {
            $(this).css({
                'border-color': 'var(--border-color)',
                'background': 'var(--bg-primary)'
            });
        }
    );

    // 初始化重新上传组件
    if ($('#reuploadBtn').length > 0) {
        try {
            var reuploadInst = upload.render({
                elem: '#reuploadBtn',
                url: '{:url("Upload/file")}',
                accept: 'file',
                exts: 'jpg|png|jpeg',
                size: 10240,
                done: function(res) {
                    console.log('Reupload response:', res);
                    if (res.code === 1) {
                        addReuploadFileToList(res.data);
                        layer.msg('文件上传成功', {icon: 1});
                    } else {
                        layer.msg('上传失败：' + (res.msg || '未知错误'), {icon: 2});
                    }
                },
                error: function() {
                    console.log('Reupload error occurred');
                    layer.msg('上传出错，请重试', {icon: 2});
                }
            });
            console.log('Reupload instance created successfully');
        } catch(e) {
            console.error('Reupload render failed:', e);
        }
    }

    // 添加重新上传文件到列表
    function addReuploadFileToList(fileData) {
        reuploadFileIndex++;
        var fileId = 'reupload_file_' + reuploadFileIndex;
        var fileSize = (fileData.size / 1024).toFixed(1) + ' KB';

        var fileHtml =
            '<div class="file-item" id="' + fileId + '" style="display: flex; align-items: center; gap: 12px; padding: 16px; background: var(--bg-primary); border: 1px solid var(--border-color); border-radius: var(--radius-md); margin-bottom: 12px;">' +
                '<div style="width: 40px; height: 40px; background: var(--success-color); border-radius: var(--radius-sm); display: flex; align-items: center; justify-content: center; color: white; font-size: 18px;">📄</div>' +
                '<div style="flex: 1;">' +
                    '<div style="font-weight: 600; color: var(--text-primary); margin-bottom: 4px;">' + fileData.name + '</div>' +
                    '<div style="font-size: 14px; color: var(--text-secondary);">' + fileSize + '</div>' +
                '</div>' +
                '<div style="display: flex; align-items: center; gap: 8px; font-size: 14px; font-weight: 500; color: var(--success-color);">' +
                    '<i class="layui-icon layui-icon-ok"></i>' +
                    '上传成功' +
                '</div>' +
                '<div>' +
                    '<button type="button" style="width: 32px; height: 32px; border: none; border-radius: var(--radius-sm); cursor: pointer; display: flex; align-items: center; justify-content: center; background: rgba(239, 68, 68, 0.1); color: var(--danger-color);" onclick="removeReuploadFile(\'' + fileId + '\', \'' + fileData.uri + '\')">' +
                        '<i class="layui-icon layui-icon-delete"></i>' +
                    '</button>' +
                '</div>' +
                '<input type="hidden" name="reuploadFilePaths[]" value="' + fileData.uri + '">' +
            '</div>';

        $('#reuploadFileList').show().append(fileHtml);
        reuploadedFiles.push({
            id: fileId,
            data: fileData
        });

        // 显示提交按钮
        $('#submitReuploadBtn').show();
    }

    // 删除重新上传的文件
    window.removeReuploadFile = function(fileId, filePath) {
        layer.confirm('确定要删除这个文件吗？', {icon: 3, title: '确认删除'}, function(index) {
            $('#' + fileId).remove();
            reuploadedFiles = reuploadedFiles.filter(function(file) {
                return file.id !== fileId;
            });

            if (reuploadedFiles.length === 0) {
                $('#reuploadFileList').hide();
                $('#submitReuploadBtn').hide();
            }

            layer.close(index);
            layer.msg('文件已删除', {icon: 1});
        });
    };

    // 提交重新上传
    $('#submitReuploadBtn').on('click', function() {
        var $btn = $(this);
        var originalText = $btn.text();

        if (reuploadedFiles.length === 0) {
            layer.msg('请先上传文件', {icon: 2});
            return false;
        }

        // 显示加载状态
        $btn.prop('disabled', true);
        $btn.html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 提交中...');

        // 收集文件路径
        var filePaths = [];
        $('input[name="reuploadFilePaths[]"]').each(function() {
            if ($(this).val()) {
                filePaths.push($(this).val());
            }
        });

        // 提交数据
        like.ajax({
            url: '{:url("Store/reupload")}',
            data: {
                id: '{$detail.id}',
                filePaths: filePaths,
                agreementNew: 1
            },
            type: 'POST',
            success: function(res) {
                if (res.code === 1) {
                    layer.msg('重新上传成功！状态已更新为待审核', {icon: 1, time: 2000}, function() {
                        // 刷新页面
                        window.location.reload();
                    });
                } else {
                    // 恢复按钮状态
                    $btn.prop('disabled', false);
                    $btn.html(originalText);
                    layer.msg(res.msg || '提交失败', {icon: 2});
                }
            },
            error: function() {
                // 恢复按钮状态
                $btn.prop('disabled', false);
                $btn.html(originalText);
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
    });
});
</script>
