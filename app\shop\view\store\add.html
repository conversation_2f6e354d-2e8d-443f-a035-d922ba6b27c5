{layout name="layout2" /}
<style>
    :root {
        --primary-color: #667eea;
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --info-color: #3b82f6;
        --bg-primary: #ffffff;
        --bg-secondary: #f8fafc;
        --bg-tertiary: #f1f5f9;
        --text-primary: #1e293b;
        --text-secondary: #64748b;
        --text-muted: #94a3b8;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        --radius-sm: 6px;
        --radius-md: 12px;
        --radius-lg: 16px;
        --radius-xl: 20px;
    }

    body {
        background: var(--bg-secondary);
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .modern-apply-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 24px;
    }

    .apply-card {
        background: var(--bg-primary);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .apply-header {
        background: var(--primary-gradient);
        color: white;
        padding: 32px;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .apply-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
    }

    .apply-title {
        font-size: 28px;
        font-weight: 700;
        margin: 0 0 12px 0;
        position: relative;
        z-index: 1;
    }

    .apply-subtitle {
        font-size: 16px;
        opacity: 0.9;
        margin: 0;
        position: relative;
        z-index: 1;
    }

    .apply-content {
        padding: 40px;
    }

    .step-indicator {
        display: flex;
        justify-content: center;
        margin-bottom: 40px;
        position: relative;
    }

    .step-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        flex: 1;
        max-width: 200px;
    }

    .step-item:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 20px;
        right: -50%;
        width: 100%;
        height: 2px;
        background: var(--border-color);
        z-index: 0;
    }

    .step-item.active:not(:last-child)::after {
        background: var(--primary-color);
    }

    .step-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--bg-tertiary);
        border: 2px solid var(--border-color);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        color: var(--text-muted);
        position: relative;
        z-index: 1;
        transition: all 0.3s ease;
    }

    .step-item.active .step-circle {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    .step-item.completed .step-circle {
        background: var(--success-color);
        border-color: var(--success-color);
        color: white;
    }

    .step-label {
        margin-top: 12px;
        font-size: 14px;
        font-weight: 500;
        color: var(--text-secondary);
        text-align: center;
    }

    .step-item.active .step-label {
        color: var(--primary-color);
        font-weight: 600;
    }

    .agreement-section {
        background: var(--bg-secondary);
        border-radius: var(--radius-lg);
        padding: 32px;
        margin-bottom: 32px;
        border: 2px dashed var(--border-color);
        transition: all 0.3s ease;
    }

    .agreement-section:hover {
        border-color: var(--primary-color);
        background: rgba(102, 126, 234, 0.02);
    }

    .agreement-header {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-bottom: 24px;
    }

    .agreement-icon {
        width: 48px;
        height: 48px;
        background: var(--primary-gradient);
        border-radius: var(--radius-md);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
    }

    .agreement-title {
        font-size: 20px;
        font-weight: 700;
        color: var(--text-primary);
        margin: 0;
    }

    .agreement-description {
        color: var(--text-secondary);
        line-height: 1.6;
        margin-bottom: 24px;
    }

    .download-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        background: var(--primary-gradient);
        color: white;
        border: none;
        border-radius: var(--radius-md);
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .download-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .download-btn:hover::before {
        left: 100%;
    }

    .download-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .checkbox-container {
        background: var(--bg-primary);
        border: 2px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: 24px;
        transition: all 0.3s ease;
    }

    .checkbox-container:hover {
        border-color: var(--primary-color);
        box-shadow: var(--shadow-md);
    }

    .checkbox-wrapper {
        display: flex;
        align-items: flex-start;
        gap: 16px;
    }

    .checkbox-content {
        flex: 1;
    }

    .checkbox-text {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 8px;
    }

    .view-agreement-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background: var(--bg-tertiary);
        color: var(--primary-color);
        border: 1px solid var(--primary-color);
        border-radius: var(--radius-md);
        font-size: 14px;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        margin-bottom: 8px;
    }

    .view-agreement-btn:hover {
        background: var(--primary-color);
        color: white;
        transform: translateY(-1px);
    }

    .checkbox-note {
        font-size: 14px;
        color: var(--text-secondary);
        line-height: 1.5;
    }

    .upload-section {
        background: var(--bg-secondary);
        border-radius: var(--radius-lg);
        padding: 32px;
        margin-bottom: 32px;
        border: 2px dashed var(--border-color);
        transition: all 0.3s ease;
    }

    .upload-section:hover {
        border-color: var(--primary-color);
        background: rgba(102, 126, 234, 0.02);
    }

    .upload-header {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-bottom: 24px;
    }

    .upload-icon {
        width: 48px;
        height: 48px;
        background: var(--success-color);
        border-radius: var(--radius-md);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
    }

    .upload-title {
        font-size: 20px;
        font-weight: 700;
        color: var(--text-primary);
        margin: 0;
    }

    .upload-description {
        color: var(--text-secondary);
        line-height: 1.6;
        margin-bottom: 24px;
    }

    .upload-area {
        border: 2px dashed var(--border-color);
        border-radius: var(--radius-lg);
        padding: 40px 20px;
        text-align: center;
        background: var(--bg-primary);
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
    }

    .upload-area:hover {
        border-color: var(--primary-color);
        background: rgba(102, 126, 234, 0.02);
    }

    .upload-area.dragover {
        border-color: var(--primary-color);
        background: rgba(102, 126, 234, 0.05);
        transform: scale(1.02);
    }

    .upload-icon-large {
        font-size: 48px;
        color: var(--text-muted);
        margin-bottom: 16px;
    }

    .upload-text {
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 8px;
    }

    .upload-subtext {
        font-size: 14px;
        color: var(--text-secondary);
        margin-bottom: 20px;
    }

    .upload-btn {
        margin-top: 16px;
    }

    .file-list {
        margin-top: 24px;
    }

    .file-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px;
        background: var(--bg-primary);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        margin-bottom: 12px;
        transition: all 0.3s ease;
    }

    .file-item:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-1px);
    }

    .file-icon {
        width: 40px;
        height: 40px;
        background: var(--success-color);
        border-radius: var(--radius-sm);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
        flex-shrink: 0;
    }

    .file-info {
        flex: 1;
    }

    .file-name {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 4px;
    }

    .file-size {
        font-size: 14px;
        color: var(--text-secondary);
    }

    .file-status {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
    }

    .file-status.success {
        color: var(--success-color);
    }

    .file-status.error {
        color: var(--danger-color);
    }

    .file-actions {
        display: flex;
        gap: 8px;
    }

    .file-action-btn {
        width: 32px;
        height: 32px;
        border: none;
        border-radius: var(--radius-sm);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .file-action-btn.delete {
        background: rgba(239, 68, 68, 0.1);
        color: var(--danger-color);
    }

    .file-action-btn.delete:hover {
        background: var(--danger-color);
        color: white;
    }

    .submit-section {
        text-align: center;
        padding-top: 24px;
        border-top: 1px solid var(--border-color);
    }

    .submit-btn {
        display: inline-flex;
        align-items: center;
        gap: 12px;
        padding: 16px 32px;
        background: var(--primary-gradient);
        color: white;
        border: none;
        border-radius: var(--radius-md);
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        min-width: 200px;
    }

    .submit-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .submit-btn:hover::before {
        left: 100%;
    }

    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
    }

    .submit-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    .loading-spinner {
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .success-message {
        background: rgba(16, 185, 129, 0.1);
        border: 1px solid rgba(16, 185, 129, 0.2);
        color: var(--success-color);
        padding: 16px;
        border-radius: var(--radius-md);
        margin-bottom: 24px;
        display: none;
    }

    @media (max-width: 768px) {
        .modern-apply-container {
            padding: 16px;
        }

        .apply-header {
            padding: 24px 16px;
        }

        .apply-content {
            padding: 24px 16px;
        }

        .step-indicator {
            flex-direction: column;
            gap: 16px;
        }

        .step-item:not(:last-child)::after {
            display: none;
        }

        .checkbox-wrapper {
            flex-direction: column;
            gap: 12px;
        }
    }
</style>

<div class="layui-card layui-form" style="box-shadow:none; display: none;">
  <div class="layui-card-body">
    <!-- 新增合同模版下载链接 -->
    <div class="layui-form-item">
      <label class="layui-form-label">协议文档:</label>
      <div class="layui-input-block">
        <a href="https://www.huohanghang.cn/uploads/other/doc/集采联盟入驻协议.doc" class="layui-btn layui-btn-primary" download="集采联盟入驻协议.doc">点击下载合同模版</a>
        <br/>
        <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">请认真阅读入驻协议内容，并签字盖上公章后上传.请勿修改文档内容,以免审核失败</div>

      </div>
    </div>
    <!-- 上传文件表单 -->
    <!-- 上传文件部分 -->
    <div class="layui-form-item">
      <input type="hidden" name="filePaths[]" id="filePathsInput">
      <label class="layui-form-label">上传合同：</label>
      <div class="layui-input-block">
        <div class="layui-upload">
          <button type="button" class="layui-btn layui-btn-sm" id="uploadBtn">选择文件</button>
          <div class="layui-upload-list">
            <table class="layui-table">
              <thead>
              <tr>
                <th>文件名</th>
                <th>大小</th>
                <th>状态</th>
              </tr>
              </thead>
              <tbody id="uploadList"></tbody>
            </table>
          </div>
          <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">需要您签字盖章上传后方可生效,请勿修改合同内容,以免审核失败</div>
        </div>
      </div>
    </div>


    <div class="layui-form-item" style="margin-bottom: 0;">
      <label class="layui-form-label">众筹协议:</label>
      <div class="layui-input-block">
        <!-- 旧的复选框已移除，使用新的现代化版本 -->
        <br/>
        <div class="layui-form-mid layui-word-aux" style="white-space:nowrap;">请认真阅读众筹协议内容，勾选“同意”即代表接受本协议所有内容。</div>

      </div>
    </div>
    <div class="layui-form-item layui-hide">
      <input type="button" lay-submit lay-filter="add-submit" id="add-submit" value="确认">
    </div>
  </div>
</div>

<div class="modern-apply-container">
    <div class="apply-card layui-form">
        <!-- 头部 -->
        <div class="apply-header">
            <h1 class="apply-title">🏢 集采购联盟入驻申请</h1>
            <p class="apply-subtitle">欢迎加入我们的集采购联盟，开启您的商业新征程</p>
        </div>

        <!-- 内容区域 -->
        <div class="apply-content">
            <!-- 步骤指示器 -->
            <div class="step-indicator">
                <div class="step-item completed">
                    <div class="step-circle">✓</div>
                    <div class="step-label">下载协议</div>
                </div>
                <div class="step-item active">
                    <div class="step-circle">2</div>
                    <div class="step-label">上传文件</div>
                </div>
                <div class="step-item">
                    <div class="step-circle">3</div>
                    <div class="step-label">确认申请</div>
                </div>
                <div class="step-item">
                    <div class="step-circle">4</div>
                    <div class="step-label">等待审核</div>
                </div>
            </div>

            <!-- 成功消息 -->
            <div class="success-message" id="successMessage">
                <strong>🎉 申请提交成功！</strong> 我们将在1-3个工作日内完成审核，请耐心等待。
            </div>

            <!-- 协议下载区域 -->
            <div class="agreement-section">
                <div class="agreement-header">
                    <div class="agreement-icon">📄</div>
                    <div>
                        <h3 class="agreement-title">《集采联盟保证金协议》文档</h3>
                    </div>
                </div>
                <p class="agreement-description">
                    请下载并仔细阅读《商家入驻集采联盟保证金协议》并签字盖章后拍照或扫描后上传。
                </p>
                <a target="_blank" href="{$domain}/uploads/documents/20250807121123f62b78925.docx"
                   class="download-btn"
                   download="集采联盟入驻协议.doc">
                    <i class="layui-icon layui-icon-download-circle"></i>
                    下载协议
                </a>
            </div>

            <!-- 文件上传区域 -->
            <div class="upload-section">
                <div class="upload-header">
                    <div class="upload-icon">📤</div>
                    <div>
                        <h3 class="upload-title">上传签署协议</h3>
                    </div>
                </div>
                <p class="upload-description">
                    请将下载的协议文档签字盖章后上传。支持jpg,png,jpeg格式，文件大小不超过 10MB。
                    请确保文档清晰可读，包含完整的签字和公章。
                </p>

                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon-large">📁</div>
                    <div class="upload-text">拖拽文件到此处或点击上传</div>
                    <div class="upload-subtext">支持 pg,png,jpeg 格式，最大 10MB</div>
                    <button type="button" class="layui-btn layui-btn-normal upload-btn" id="uploadBtnNew">
                        <i class="layui-icon layui-icon-upload"></i>
                        选择文件
                    </button>
                </div>

                <div class="file-list" id="fileList" style="display: none;">
                    <!-- 文件列表将在这里动态生成 -->
                </div>
            </div>

            <!-- 协议确认区域 -->
            <div class="checkbox-container">
                <div class="checkbox-wrapper">
                    <div class="checkbox-content">
                        <div class="checkbox-text">我已阅读并同意入驻协议</div>
                        <div style="margin: 16px 0;">
                            <input type="checkbox" name="agreementNew" value="1" lay-verify="required" lay-filter="agreementCheckboxNew" title="我已阅读并同意入驻协议" id="agreementCheck">
                        </div>
                        <a href="javascript:void(0);" class="view-agreement-btn" id="agreementLinkNew">
                            <i class="layui-icon layui-icon-read"></i>
                            在线查看《商家入驻集采联盟协议》
                        </a>
                        <div class="checkbox-note">
                            请认真阅读协议内容，勾选"同意"即代表您接受本协议的所有条款和条件。
                            提交申请后，我们将根据协议内容为您提供相应的服务。
                        </div>
                    </div>
                </div>
            </div>

            <!-- 提交区域 -->
            <div class="submit-section">
                <button type="button" class="submit-btn" id="submitBtn" lay-submit lay-filter="add-submit">
                    <i class="layui-icon layui-icon-ok"></i>
                    <span class="btn-text">提交入驻申请</span>
                </button>
            </div>
        </div>

        <!-- 隐藏的提交按钮 -->
        <div class="layui-form-item layui-hide">
            <input type="button" lay-submit lay-filter="add-submit" id="add-submit" value="确认">
        </div>
    </div>
</div>

<script>
  layui.use(['layer','form','upload'], function () {
    var layer = layui.layer;
    var form = layui.form;
    var upload = layui.upload;

    // 全局变量
    var uploadedFiles = [];
    var fileIndex = 0;

    // 提交按钮交互
    $('#submitBtn').on('click', function() {
        var $btn = $(this);
        var $btnText = $btn.find('.btn-text');
        var originalText = $btnText.text();

        // 检查文件是否已上传
        if (uploadedFiles.length === 0) {
            layer.msg('请先上传签署后的协议文档', {icon: 2});
            return false;
        }

        // 检查协议是否已勾选
        if (!$('input[name="agreementNew"]:checked').length) {
            layer.msg('请先阅读并同意入驻协议', {icon: 2});
            return false;
        }

        // 显示加载状态
        $btn.prop('disabled', true);
        $btnText.text('提交中...');
        $btn.find('i').removeClass('layui-icon-ok').addClass('loading-spinner');

        // 准备提交数据
        var formData = {
            agreementNew: $('input[name="agreementNew"]:checked').val() || '',
            filePaths: []
        };

        // 收集上传的文件路径
        $('input[name="filePaths[]"]').each(function() {
            if ($(this).val()) {
                formData.filePaths.push($(this).val());
            }
        });

        // 真实提交申请
        like.ajax({
            url: '{:url("Store/add")}',
            data: formData,
            type: 'POST',
            success: function(res) {
                if (res.code === 1) {
                    // 更新步骤指示器 - 完成所有步骤
                    $('.step-item').removeClass('active').addClass('completed');
                    $('.step-item').last().addClass('active');

                    // 恢复按钮状态
                    $btn.prop('disabled', false);
                    $btnText.text('申请已提交');
                    $btn.find('i').removeClass('loading-spinner').addClass('layui-icon-ok');

                    layer.msg('申请提交成功！正在跳转到支付页面...', {icon: 1, time: 2000}, function() {
                        // 跳转到支付页面
                        window.location.href = '{:url("Store/jclist")}';
                    });
                } else {
                    // 恢复按钮状态
                    $btn.prop('disabled', false);
                    $btnText.text('提交入驻申请');
                    $btn.find('i').removeClass('loading-spinner').addClass('layui-icon-ok');

                    layer.msg(res.msg || '提交失败', {icon: 2});
                }
            },
            error: function() {
                // 恢复按钮状态
                $btn.prop('disabled', false);
                $btnText.text('提交入驻申请');
                $btn.find('i').removeClass('loading-spinner').addClass('layui-icon-ok');

                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
    });

    // 渲染表单组件
    form.render();

    // 监听复选框变化
    form.on('checkbox(agreementCheckboxNew)', function(data) {
        console.log('复选框状态:', data.elem.checked);
    });

    // 等待DOM完全加载后初始化上传组件
    $(document).ready(function() {
        // 检查按钮是否存在
        console.log('Upload button exists:', $('#uploadBtnNew').length);

        // 文件上传功能 - 使用最简配置
        try {
            var uploadInst = upload.render({
                elem: '#uploadBtnNew'
                ,url: '{:url("Upload/file")}'
                ,accept: 'file'
                ,exts: 'jpg|png|jpeg'
                ,size: 10240
                ,done: function(res) {
                    console.log('Upload response:', res);
                    if (res.code === 1) {
                        addFileToList(res.data);
                        updateStepIndicator();
                        layer.msg('文件上传成功', {icon: 1});
                    } else {
                        layer.msg('上传失败：' + (res.msg || '未知错误'), {icon: 2});
                    }
                }
                ,error: function() {
                    console.log('Upload error occurred');
                    layer.msg('上传出错，请重试', {icon: 2});
                }
            });
            console.log('Upload render success');
        } catch(e) {
            console.error('Upload render failed:', e);
        }

        console.log('Upload instance created:', uploadInst);

        // 手动绑定点击事件作为备用方案
        $('#uploadBtnNew').on('click', function() {
            console.log('Button clicked manually');
        });

        // 测试按钮是否可点击
        setTimeout(function() {
            console.log('Button element:', $('#uploadBtnNew')[0]);
            console.log('Button visible:', $('#uploadBtnNew').is(':visible'));
            console.log('Button disabled:', $('#uploadBtnNew').prop('disabled'));
        }, 1000);
    }); // 结束 document.ready

    // 添加文件到列表
    function addFileToList(fileData) {
        fileIndex++;
        var fileId = 'file_' + fileIndex;
        var fileSize = (fileData.size / 1024).toFixed(1) + ' KB';

        var fileHtml =
            '<div class="file-item" id="' + fileId + '">' +
                '<div class="file-icon">📄</div>' +
                '<div class="file-info">' +
                    '<div class="file-name">' + fileData.name + '</div>' +
                    '<div class="file-size">' + fileSize + '</div>' +
                '</div>' +
                '<div class="file-status success">' +
                    '<i class="layui-icon layui-icon-ok"></i>' +
                    '上传成功' +
                '</div>' +
                '<div class="file-actions">' +
                    '<button type="button" class="file-action-btn delete" onclick="removeFile(\'' + fileId + '\', \'' + fileData.uri + '\')">' +
                        '<i class="layui-icon layui-icon-delete"></i>' +
                    '</button>' +
                '</div>' +
                '<input type="hidden" name="filePaths[]" value="' + fileData.uri + '">' +
            '</div>';

        $('#fileList').show().append(fileHtml);
        uploadedFiles.push({
            id: fileId,
            data: fileData
        });
    }

    // 删除文件
    window.removeFile = function(fileId, filePath) {
        layer.confirm('确定要删除这个文件吗？', {icon: 3, title: '确认删除'}, function(index) {
            $('#' + fileId).remove();
            uploadedFiles = uploadedFiles.filter(function(file) {
                return file.id !== fileId;
            });

            if (uploadedFiles.length === 0) {
                $('#fileList').hide();
                updateStepIndicator();
            }

            layer.close(index);
            layer.msg('文件已删除', {icon: 1});
        });
    };

    // 更新步骤指示器
    function updateStepIndicator() {
        if (uploadedFiles.length > 0) {
            // 文件已上传，激活第3步
            $('.step-item').eq(1).removeClass('active').addClass('completed');
            $('.step-item').eq(2).addClass('active');
        } else {
            // 没有文件，回到第2步
            $('.step-item').eq(1).removeClass('completed').addClass('active');
            $('.step-item').eq(2).removeClass('active');
        }
    }

    // 显示协议弹窗
    $('#agreementLinkNew').on('click', function() {
        var agreementContent =
            '<div style="padding: 40px 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 16px 16px 0 0; text-align: center;">' +
                '<h2 style="margin: 0 0 16px 0; font-size: 24px; font-weight: 700;">📋 商家入驻集采联盟协议</h2>' +
                '<p style="margin: 0; opacity: 0.9; font-size: 16px;">请仔细阅读以下协议条款</p>' +
            '</div>' +
            '<div id="agreementContent" style="padding: 30px; max-height: 400px; overflow-y: auto; line-height: 1.8; color: #333;">' +
                '<div style="text-align: center; padding: 60px 20px; color: #94a3b8;">' +
                    '<div style="font-size: 48px; margin-bottom: 16px;">📄</div>' +
                    '<div style="font-size: 18px; margin-bottom: 8px;">正在加载协议内容...</div>' +
                    '<div style="font-size: 14px;">请稍候</div>' +
                '</div>' +
            '</div>';

        layer.open({
            type: 1,
            title: false,
            area: ['800px', '600px'],
            content: agreementContent,
            btn: ['我已阅读并理解', '关闭'],
            btnAlign: 'c',
            closeBtn: 1,
            yes: function(index, layero){
                // 自动勾选协议复选框
                $('input[name="agreementNew"]').prop('checked', true);
                form.render('checkbox'); // 重新渲染复选框
                layer.msg('已确认您已阅读协议内容', {icon: 1});
                layer.close(index);
            },
            btn2: function(index, layero) {
                layer.close(index);
            },
            success: function(layero, index) {
                // 加载协议内容
                fetch("{:url('jcai.Activity/agreementContent')}")
                    .then(response => response.text())
                    .then(data => {
                        $('#agreementContent').html(data || '<div style="padding: 20px; text-align: center; color: #64748b;">协议内容加载失败，请稍后重试</div>');
                    })
                    .catch(error => {
                        console.error('Error loading agreement:', error);
                        $('#agreementContent').html('<div style="padding: 20px; text-align: center; color: #ef4444;">协议内容加载失败，请稍后重试</div>');
                    });
            }
        });

        return false;
    });

    // 表单验证
    form.verify({
      agreementCheckbox: function(value, item){
        if (uploadedFiles.length === 0) {
          return '请先上传签署后的协议文档';
        }
        if(!$('input[name="agreementNew"]:checked').length){
          return '您必须同意入驻协议';
        }
      }
    });

    // 监听表单提交
    form.on('submit(add-submit)', function(data){
      // 触发现代化提交按钮的点击事件
      $('#submitBtn').click();
      return false; // 阻止默认提交
    });

    // 页面加载完成后的初始化
    $(document).ready(function() {
        // 添加页面加载动画
        $('.modern-apply-container').hide().fadeIn(800);

        // 添加步骤指示器动画
        $('.step-item').each(function(index) {
            $(this).delay(index * 200).animate({opacity: 1}, 500);
        });

        // 添加协议区域悬停效果
        $('.agreement-section').hover(
            function() {
                $(this).css('transform', 'translateY(-2px)');
            },
            function() {
                $(this).css('transform', 'translateY(0)');
            }
        );
    });
  })
</script>