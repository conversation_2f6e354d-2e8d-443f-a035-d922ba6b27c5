{layout name="layout1" /}
<style>
    :root {
        --primary-color: #667eea;
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --info-color: #3b82f6;
        --bg-primary: #ffffff;
        --bg-secondary: #f8fafc;
        --bg-tertiary: #f1f5f9;
        --text-primary: #1e293b;
        --text-secondary: #64748b;
        --text-muted: #94a3b8;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        --radius-sm: 6px;
        --radius-md: 12px;
        --radius-lg: 16px;
        --radius-xl: 20px;
    }

    body {
        background: var(--bg-secondary);
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .modern-wrapper {
        padding: 24px;
        max-width: 1400px;
        margin: 0 auto;
    }

    .modern-card {
        background: var(--bg-primary);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .modern-card:hover {
        box-shadow: var(--shadow-xl);
        transform: translateY(-2px);
    }

    .card-header {
        background: var(--primary-gradient);
        color: white;
        padding: 32px;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .card-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
    }

    .card-title {
        font-size: 28px;
        font-weight: 700;
        margin: 0 0 16px 0;
        position: relative;
        z-index: 1;
    }

    .card-subtitle {
        font-size: 16px;
        opacity: 0.9;
        margin: 0;
        position: relative;
        z-index: 1;
    }

    .deposit-info {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border-radius: var(--radius-md);
        padding: 24px;
        margin: 24px 0;
        border: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 1;
    }

    .deposit-amount {
        font-size: 36px;
        font-weight: 800;
        color: #fbbf24;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin: 0 8px;
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 8px 16px;
        border-radius: 50px;
        font-size: 14px;
        font-weight: 600;
        margin: 8px 4px;
        transition: all 0.3s ease;
    }

    .status-badge.success {
        background: rgba(16, 185, 129, 0.1);
        color: var(--success-color);
        border: 1px solid rgba(16, 185, 129, 0.2);
    }

    .status-badge.warning {
        background: rgba(245, 158, 11, 0.1);
        color: var(--warning-color);
        border: 1px solid rgba(245, 158, 11, 0.2);
    }

    .status-badge.info {
        background: rgba(59, 130, 246, 0.1);
        color: var(--info-color);
        border: 1px solid rgba(59, 130, 246, 0.2);
    }

    .modern-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        border-radius: var(--radius-md);
        font-weight: 600;
        font-size: 14px;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        position: relative;
        overflow: hidden;
    }

    .modern-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .modern-btn:hover::before {
        left: 100%;
    }

    .modern-btn.primary {
        background: var(--primary-gradient);
        color: white;
        box-shadow: var(--shadow-md);
    }

    .modern-btn.primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .modern-btn.success {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        box-shadow: var(--shadow-md);
    }

    .modern-btn.warning {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
        box-shadow: var(--shadow-md);
    }

    .search-section {
        background: var(--bg-primary);
        border-radius: var(--radius-lg);
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
    }

    .search-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 16px;
        align-items: end;
    }

    .form-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .form-label {
        font-weight: 600;
        color: var(--text-primary);
        font-size: 14px;
    }

    .form-input {
        padding: 12px 16px;
        border: 2px solid var(--border-color);
        border-radius: var(--radius-md);
        font-size: 14px;
        transition: all 0.3s ease;
        background: var(--bg-primary);
    }

    .form-input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .table-container {
        background: var(--bg-primary);
        border-radius: var(--radius-lg);
        overflow: hidden;
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
    }

    .table-header {
        background: var(--bg-tertiary);
        padding: 20px 24px;
        border-bottom: 1px solid var(--border-color);
    }

    .table-title {
        font-size: 20px;
        font-weight: 700;
        color: var(--text-primary);
        margin: 0;
    }

    .layui-table-cell {
        height: auto;
        padding: 16px 12px;
        border-bottom: 1px solid var(--border-color);
    }

    .layui-table th {
        background: var(--bg-tertiary);
        color: var(--text-primary);
        font-weight: 600;
        border-bottom: 2px solid var(--border-color);
    }

    .layui-table tr:hover {
        background: var(--bg-secondary);
    }

    .payment-method {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 6px 12px;
        background: var(--bg-secondary);
        border-radius: var(--radius-sm);
        font-weight: 500;
    }

    .amount-display {
        font-size: 18px;
        font-weight: 700;
        color: var(--success-color);
    }

    .date-display {
        color: var(--text-secondary);
        font-size: 14px;
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .loading-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    .loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: var(--text-muted);
    }

    .empty-state-icon {
        font-size: 64px;
        margin-bottom: 16px;
        opacity: 0.5;
    }

    .empty-state-text {
        font-size: 18px;
        margin-bottom: 8px;
    }

    .empty-state-subtext {
        font-size: 14px;
    }

    @media (max-width: 768px) {
        .modern-wrapper {
            padding: 16px;
        }

        .card-header {
            padding: 24px 16px;
        }

        .card-title {
            font-size: 24px;
        }

        .deposit-amount {
            font-size: 28px;
        }

        .search-grid {
            grid-template-columns: 1fr;
        }

        .modern-btn {
            width: 100%;
            justify-content: center;
        }
    }
</style>

<div class="modern-wrapper">
    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- 主卡片 -->
    <div class="modern-card">
        <!-- 卡片头部 -->
        <div class="card-header">
            <h1 class="card-title">🏢 集采购联盟入驻中心</h1>
            <p class="card-subtitle">加入我们的集采购联盟，享受更多商业机会与优质服务</p>

            <div class="deposit-info">
                <div id="depositSection">
                    <div style="display: flex; align-items: center; justify-content: center; flex-wrap: wrap; gap: 16px;">
                        <span style="font-size: 18px; font-weight: 600;">入驻保证金：</span>
                        <span class="deposit-amount">{$depositAmount|default=1000}¥</span>
                        {if !empty($deposit) && $deposit['deposit_amount'] <= 0}
                            <span style="color: #ff4757; font-size: 14px; margin-left: 8px;">(当前记录金额异常，需要修复)</span>
                        {/if}

                        {if !empty($deposit) && $deposit['pay_status']==0 && $deposit['status']!=2}
                            <span class="status-badge warning">⏳ 待支付</span>
                            {if $deposit['deposit_amount'] <= 0}
                                <button class="modern-btn warning fix-deposit-amount" data-deposit-id="{$deposit.id}">
                                    <i class="layui-icon layui-icon-set"></i>
                                    修复保证金金额
                                </button>
                            {else}
                                <button class="modern-btn primary pay-deposit" data-deposit-id="{$deposit.id}">
                                    <i class="layui-icon layui-icon-rmb"></i>
                                    立即缴费
                                </button>
                            {/if}
                        {elseif !empty($deposit) && $deposit['pay_status']==1 && $deposit['status']==0}
                            <span class="status-badge info">⏳ 待审核</span>
                            <button class="modern-btn info view-status" data-deposit-id="{$deposit.id}">
                                <i class="layui-icon layui-icon-search"></i>
                                查看状态
                            </button>
                        {elseif !empty($deposit) && $deposit['pay_status']==1 && $deposit['status']==2}
                            <span class="status-badge info">� 可修改</span>
                            <button class="modern-btn warning add-ad">
                                <i class="layui-icon layui-icon-edit"></i>
                                修改信息
                            </button>
                        {elseif !empty($deposit) && $deposit['pay_status']==1 && $deposit['status']==1}
                            <span class="status-badge success">✅ 已入驻</span>
                            <div style="font-size: 16px; font-weight: 600; margin-top: 8px;">
                                🎉 恭喜您已成为尊贵的集采联盟会员商家！
                            </div>
                        {elseif empty($deposit)}
                            <span class="status-badge info">🚀 可申请</span>
                            <button class="modern-btn success add-ad">
                                <i class="layui-icon layui-icon-add-1"></i>
                                申请入驻
                            </button>
                        {else}
                            <span class="status-badge warning">❓ 状态异常</span>
                            <button class="modern-btn info view-status" data-deposit-id="{$deposit.id}">
                                <i class="layui-icon layui-icon-search"></i>
                                查看详情
                            </button>
                        {/if}
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索筛选区域 -->
        <!-- <div class="search-section">
            <div class="search-grid">
                <div class="form-group">
                    <label class="form-label">支付方式</label>
                    <select class="form-input" id="paymentMethodFilter">
                        <option value="">全部支付方式</option>
                        <option value="微信支付">微信支付</option>
                        <option value="支付宝">支付宝</option>
                        <option value="银行转账">银行转账</option>
                        <option value="现金">现金</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">审核状态</label>
                    <select class="form-input" id="statusFilter">
                        <option value="">全部状态</option>
                        <option value="0">待审核</option>
                        <option value="1">审核通过</option>
                        <option value="2">审核不通过</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">支付时间</label>
                    <input type="text" class="form-input" id="dateRange" placeholder="选择时间范围">
                </div>
                <div class="form-group">
                    <label class="form-label" style="opacity: 0;">操作</label>
                    <button class="modern-btn primary" id="searchBtn">
                        <i class="layui-icon layui-icon-search"></i>
                        搜索
                    </button>
                </div>
            </div>
        </div> -->

        <!-- 表格容器 -->
        <div class="table-container">
            <div class="table-header">
                <h3 class="table-title">📋 入驻记录</h3>
            </div>
            <table id="ad-lists" lay-filter="ad-lists"></table>
        </div>
    </div>

    <!-- 模板脚本 -->
    <script type="text/html" id="operation">
        <div style="text-align: left; margin-left: 10px;">
            <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="viewDetail({{d.id}})">
                <i class="layui-icon layui-icon-list"></i> 明细
            </button>
            <button class="layui-btn layui-btn-sm layui-btn-primary" onclick="viewDocs({{d.id}})">
                <i class="layui-icon layui-icon-file"></i> 协议
            </button>
        </div>
    </script>

    <script type="text/html" id="image">
        <img src="{{d.image}}" style="height: 80px; width: 80px; border-radius: 8px; object-fit: cover;" class="image-show">
    </script>

    <script type="text/html" id="paymentMethodTpl">
        <div class="payment-method">
            <i class="layui-icon layui-icon-rmb"></i>
            {{d.payment_method}}
        </div>
    </script>

    <script type="text/html" id="amountTpl">
        <div class="amount-display">¥{{d.deposit_amount}}</div>
    </script>

    <script type="text/html" id="dateTpl">
        <div class="date-display">{{d.payment_date}}</div>
    </script>

    <script type="text/html" id="statusTpl">
        {{# if(d.status == 0) { }}
            <span class="status-badge warning">⏳ 待审核</span>
        {{# } else if(d.status == 1) { }}
            <span class="status-badge success">✅ 审核通过</span>
        {{# } else { }}
            <span class="status-badge danger" style="background: rgba(239, 68, 68, 0.1); color: var(--danger-color); border: 1px solid rgba(239, 68, 68, 0.2);">❌ 审核不通过</span>
        {{# } }}
    </script>
</div>
<script>
    layui.config({
        version:"{$front_version|default='1.0.0'}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form'], function(){
        var $ = layui.$
            , form = layui.form
            , table = layui.table
            , element = layui.element
            , laydate = layui.laydate;

        //图片放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,400);
        });

        // 初始化日期选择器
        laydate.render({
            elem: '#dateRange',
            range: '~',
            type: 'datetime'
        });

        // 获取列表
        getList();

        function getList(searchParams = {}) {
            showLoading();

            table.render({
                elem: '#ad-lists',
                url: '{:url("store/lists")}',
                where: searchParams,
                cols: [[
                    {field: 'id', title: 'ID', width: 80, align: 'center', sort: true},
                    {field: 'payment_method', title: '支付方式', align: 'center', width: 180, templet: '#paymentMethodTpl'},
                    {field: 'deposit_amount', title: '保证金金额', align: 'center', width: 150, templet: '#amountTpl', sort: true},
                    {field: 'payment_date', title: '支付时间', align: 'center', width: 200, templet: '#dateTpl', sort: true},
                    {field: 'status', title: '审核状态', align: 'center', width: 150, templet: '#statusTpl'},
                    {field: 'created_at', title: '创建时间', align: 'center', width: 180, sort: true},
                    {fixed: 'right', title: '操作', width: 200, align: 'center', toolbar: '#operation'}
                ]],
                page: true,
                limit: 15,
                limits: [10, 15, 20, 30, 50],
                text: {
                    none: '<div class="empty-state"><div class="empty-state-icon">📋</div><div class="empty-state-text">暂无入驻记录</div><div class="empty-state-subtext">您还没有任何入驻记录，快去申请入驻吧！</div></div>'
                },
                response: {
                    statusCode: 1
                },
                parseData: function (res) {
                    hideLoading();
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.data.count,
                        "data": res.data.lists,
                    };
                },
                done: function(res, curr, count) {
                    hideLoading();
                    // 解决操作栏因为内容过多换行问题
                    $(".layui-table-main tr").each(function (index, val) {
                        $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                        $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                    });

                    // 添加表格行悬停效果
                    $('.layui-table-body tr').hover(
                        function() {
                            $(this).css('background-color', 'var(--bg-secondary)');
                        },
                        function() {
                            $(this).css('background-color', '');
                        }
                    );
                }
            });
        }

        // 搜索功能
        $('#searchBtn').on('click', function() {
            var searchParams = {
                payment_method: $('#paymentMethodFilter').val(),
                status: $('#statusFilter').val(),
                date_range: $('#dateRange').val()
            };
            getList(searchParams);
        });

        // 回车搜索
        $('.form-input').on('keypress', function(e) {
            if (e.which === 13) {
                $('#searchBtn').click();
            }
        });

        // 加载和隐藏loading
        function showLoading() {
            $('#loadingOverlay').addClass('show');
        }

        function hideLoading() {
            $('#loadingOverlay').removeClass('show');
        }
        // 支付相关变量
        var paymentPollInterval = null;
        var depositId = '{$deposit.id??0}';

        // 绑定支付事件
        $(document).on('click', '.pay-deposit', function(){
            var $btn = $(this);
            $btn.prop('disabled', true).html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 处理中...');

            // 发起支付请求
            $.ajax({
                url: '/api/pay/unifiedpay',
                type: 'POST',
                data: {
                    from: 'bondcharge',
                    order_id: depositId,
                    pay_way: 1,
                    client: 5
                },
                success: function(response){
                    $btn.prop('disabled', false).html('<i class="layui-icon layui-icon-rmb"></i> 立即缴费');

                    if(response.code === 1){
                        var qrCode = response.data;
                        showQRCode(qrCode);
                        startPaymentPolling();
                    } else {
                        layer.msg('支付请求失败：' + response.msg, {icon: 2});
                    }
                },
                error: function(){
                    $btn.prop('disabled', false).html('<i class="layui-icon layui-icon-rmb"></i> 立即缴费');
                    layer.msg('支付请求异常，请稍后重试', {icon: 2});
                }
            });
        });

        // 开始支付状态轮询
        function startPaymentPolling() {
            if (paymentPollInterval) {
                clearInterval(paymentPollInterval);
            }

            paymentPollInterval = setInterval(function() {
                checkPaymentStatus();
            }, 3000); // 每3秒轮询一次

            // 5分钟后停止轮询
            setTimeout(function() {
                if (paymentPollInterval) {
                    clearInterval(paymentPollInterval);
                    paymentPollInterval = null;
                }
            }, 300000);
        }

        // 轮询检查支付状态的函数
        function checkPaymentStatus() {
            $.ajax({
                url: '/shop/store/getPayStatus',
                type: 'GET',
                data: {
                    id: depositId,
                    from: 'trade'
                },
                success: function(response) {
                    if (response.code === 1 && response.data.status === 'paid') {
                        // 支付成功
                        if (paymentPollInterval) {
                            clearInterval(paymentPollInterval);
                            paymentPollInterval = null;
                        }

                        layer.closeAll(); // 关闭所有弹窗
                        layer.msg('🎉 支付成功！正在刷新页面...', {
                            icon: 1,
                            time: 2000
                        }, function() {
                            location.reload();
                        });
                    } else if (response.code === 1 && response.data.status !== 'paid') {
                        // 支付未完成，继续轮询
                        console.log('支付状态检查中...');
                    } else {
                        // 查询支付状态失败
                        if (paymentPollInterval) {
                            clearInterval(paymentPollInterval);
                            paymentPollInterval = null;
                        }
                        layer.msg('查询支付状态失败：' + response.msg, {icon: 2});
                    }
                },
                error: function() {
                    console.log('支付状态查询请求异常，继续轮询...');
                }
            });
        }

        // 显示炫酷的支付二维码弹窗
        function showQRCode(qrCode) {
            layer.open({
                type: 1,
                title: false,
                area: ['420px', '580px'],
                content: '<div style="position: relative; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 20px; overflow: hidden; color: white; min-height: 580px; display: flex; flex-direction: column; align-items: center; padding: 0;"><div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; overflow: hidden; z-index: 0;"><div style="position: absolute; width: 80px; height: 80px; top: 10%; left: 10%; border-radius: 50%; background: rgba(255, 255, 255, 0.1); animation: qr-float1 6s ease-in-out infinite;"></div><div style="position: absolute; width: 120px; height: 120px; top: 60%; right: 10%; border-radius: 50%; background: rgba(255, 255, 255, 0.1); animation: qr-float2 6s ease-in-out infinite 2s;"></div><div style="position: absolute; width: 60px; height: 60px; bottom: 20%; left: 20%; border-radius: 50%; background: rgba(255, 255, 255, 0.1); animation: qr-float3 6s ease-in-out infinite 4s;"></div></div><div style="position: relative; z-index: 1; text-align: center; padding: 30px 30px 15px;"><div style="width: 50px; height: 50px; background: rgba(255, 255, 255, 0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; backdrop-filter: blur(10px); animation: qr-pulse 2s ease-in-out infinite;"><svg width="30" height="30" viewBox="0 0 24 24" fill="none"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/></svg></div><h2 style="font-size: 24px; font-weight: 700; margin: 0 0 8px 0; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);">微信扫码支付</h2><p style="font-size: 14px; opacity: 0.9; margin: 0; font-weight: 400;">请使用微信扫描下方二维码完成支付</p></div><div style="position: relative; z-index: 1; padding: 15px;"><div style="position: relative; display: inline-block;"><div style="background: white; padding: 15px; border-radius: 15px; box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3); position: relative; overflow: hidden;"><div style="position: absolute; top: -2px; left: -2px; right: -2px; bottom: -2px; background: linear-gradient(45deg, #00d4ff, #ff00d4, #00ff88, #ffaa00); border-radius: 17px; z-index: -1; animation: qr-border-rotate 3s linear infinite;"></div><img src="' + qrCode + '" style="width: 180px; height: 180px; display: block; border-radius: 8px;" alt="支付二维码" /></div><div style="position: absolute; top: 15px; left: 15px; right: 15px; height: 2px; background: linear-gradient(90deg, transparent, #00ff88, transparent); animation: qr-scan 2s ease-in-out infinite;"></div></div></div><div style="position: relative; z-index: 1; background: rgba(255, 255, 255, 0.15); backdrop-filter: blur(10px); border-radius: 12px; padding: 15px 25px; margin: 15px 25px; text-align: center; border: 1px solid rgba(255, 255, 255, 0.2);"><div style="font-size: 12px; opacity: 0.8; margin-bottom: 5px;">支付金额</div><div style="font-size: 26px; font-weight: 700; color: #00ff88; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);">¥{$depositAmount|default=0.01}</div></div><div style="position: relative; z-index: 1; display: flex; align-items: center; justify-content: center; gap: 8px; margin: 15px 0;"><div style="width: 10px; height: 10px; background: #00ff88; border-radius: 50%; animation: qr-blink 1.5s ease-in-out infinite;"></div><span style="font-size: 14px; font-weight: 500;">等待支付中...</span></div><div style="position: relative; z-index: 1; display: flex; justify-content: space-around; width: 100%; padding: 15px 25px 25px; margin-top: auto;"><div style="display: flex; flex-direction: column; align-items: center; gap: 5px; font-size: 11px; opacity: 0.8; text-align: center;"><span style="font-size: 16px;">📱</span><span>打开微信</span></div><div style="display: flex; flex-direction: column; align-items: center; gap: 5px; font-size: 11px; opacity: 0.8; text-align: center;"><span style="font-size: 16px;">📷</span><span>扫描二维码</span></div><div style="display: flex; flex-direction: column; align-items: center; gap: 5px; font-size: 11px; opacity: 0.8; text-align: center;"><span style="font-size: 16px;">💰</span><span>确认支付</span></div></div></div><style>@keyframes qr-float1 { 0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; } 50% { transform: translateY(-15px) rotate(180deg); opacity: 0.3; } } @keyframes qr-float2 { 0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; } 50% { transform: translateY(-12px) rotate(-180deg); opacity: 0.3; } } @keyframes qr-float3 { 0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; } 50% { transform: translateY(-18px) rotate(90deg); opacity: 0.3; } } @keyframes qr-pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.05); } } @keyframes qr-border-rotate { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } } @keyframes qr-scan { 0% { transform: translateY(0); opacity: 1; } 100% { transform: translateY(180px); opacity: 0; } } @keyframes qr-blink { 0%, 100% { opacity: 1; transform: scale(1); } 50% { opacity: 0.3; transform: scale(0.8); } }</style>',

                closeBtn: 1,
                shadeClose: false,
                yes: function (index, layero) {
                    // 用户点击“确认支付”后，可以在这里添加支付成功后的回调逻辑
                    // 注意：这里只是模拟用户确认支付，实际的支付成功回调应由支付平台通知
                    layer.msg('支付成功！');
                    // 刷新页面或执行其他操作...
                    location.reload(); // 示例：刷新页面
                    layer.close(index);
                },
                btn2: function (index, layero) {
                    // 用户点击“取消”
                    layer.close(index);
                }
            });
        }
        //监听工具条
        table.on('tool(ad-lists)', function (obj) {
            var id = obj.data.id;
            if (obj.event === 'status') {
                like.ajax({
                    url: '{:url("decoration.ad/status")}?id=' + obj.data.id + '&status=0',
                    data: {},
                    type: "post",
                    success: function (res) {
                        if (res.code == 1) {
                            layui.layer.msg(res.msg, {
                                offset: '15px'
                                , icon: 1
                                , time: 1000
                            }, function () {
                                location.reload();
                            });
                        }
                    }
                });
            }
            if (obj.event === 'delete') {
                layer.confirm('确定删除?', {icon: 3, title:'提示'}, function(index) {
                    layer.close(index);
                    like.ajax({
                        url: '{:url("decoration.ad/delete")}?id=' + obj.data.id,
                        data: {},
                        type: "post",
                        success: function (res) {
                            if (res.code == 1) {
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                }, function () {
                                    location.reload();
                                });
                            }
                        }
                    });
                });
            }
            if (obj.event === 'edit') {
                var index_add_edit = layer.open({
                    type: 2
                    , title: '编辑广告'
                    , content: '{:url("decoration.ad/edit")}?id=' + obj.data.id
                    , area: ['90%', '90%']
                    , btn: ['确定', '取消']
                    , yes: function (index, layero) {
                        var iframeWindow = window['layui-layer-iframe' + index]
                            , submitID = 'edit-submit'
                            , submit = layero.find('iframe').contents().find('#' + submitID);

                        //监听提交
                        iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                            var field = data.field; //获取提交的字段
                            like.ajax({
                                url: '{:url("decoration.ad/edit")}?id=' + obj.data.id,
                                data: field,
                                type: "post",
                                success: function (res) {
                                    if (res.code == 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        }, function () {
                                            layer.close(index); //关闭弹层
                                            location.reload();//刷新
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                })
            }
        });
        // 新增
        $(document).on('click', '.add-ad', function () {
            var index_add_edit = layer.open({
                type: 2
                , title: '入驻集采联盟申请页'
                , content: '{:url("store/add")}'
                , area: ['90%', '90%']
                , btn: ['确认', '返回']
                , yes: function (index, layero) {
                    var iframeWindow = window['layui-layer-iframe' + index]
                        , submitID = 'add-submit'
                        , submit = layero.find('iframe').contents().find('#' + submitID);
                    //监听提交
                    iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                        var field = data.field;
                        console.log(data);
                        like.ajax({
                            url: '{:url("store/add")}',
                            data: field,
                            type: "post",
                            success: function (res) {
                                if (res.code == 1) {
                                    layui.layer.msg(res.msg, {
                                        offset: '15px'
                                        , icon: 1
                                        , time: 1000
                                    });
                                    layer.close(index); //关闭弹层
                                    location.reload();//刷新
                                }
                            }
                        });
                    });
                    // 触发子窗口表单提交事件
                    submit.trigger('click');
                }
            })
        });



        // 查看状态
        $(document).on('click', '.view-status', function () {
            var depositId = $(this).data('deposit-id');

            layer.open({
                type: 2,
                title: '查看申请状态',
                content: '{:url("Store/status")}?id=' + depositId,
                area: ['80%', '70%'],
                btn: ['关闭'],
                yes: function(index) {
                    layer.close(index);
                }
            });
        });

        // 修复保证金金额
        $(document).on('click', '.fix-deposit-amount', function () {
            var depositId = $(this).data('deposit-id');
            var $btn = $(this);

            layer.confirm('检测到保证金金额为0，是否修复为系统配置的金额？', {
                icon: 3,
                title: '修复保证金金额',
                btn: ['确认修复', '取消']
            }, function(index) {
                $btn.prop('disabled', true).html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 修复中...');

                like.ajax({
                    url: '{:url("Store/fixDepositAmount")}',
                    data: {
                        id: depositId
                    },
                    type: 'POST',
                    success: function(res) {
                        if (res.code === 1) {
                            layer.msg(res.msg, {icon: 1, time: 2000}, function() {
                                // 刷新页面
                                window.location.reload();
                            });
                        } else {
                            $btn.prop('disabled', false).html('<i class="layui-icon layui-icon-set"></i> 修复保证金金额');
                            layer.msg(res.msg || '修复失败', {icon: 2});
                        }
                    },
                    error: function() {
                        $btn.prop('disabled', false).html('<i class="layui-icon layui-icon-set"></i> 修复保证金金额');
                        layer.msg('网络错误，请重试', {icon: 2});
                    }
                });

                layer.close(index);
            });
        });
    });

    // 查看明细
    function viewDetail(depositId) {
        layer.open({
            type: 2,
            title: '保证金明细',
            content: '{:url("Store/depositDetail")}?deposit_id=' + depositId,
            area: ['90%', '80%'],
            btn: ['关闭'],
            yes: function(index) {
                layer.close(index);
            }
        });
    }

    // 查看协议
    function viewDocs(depositId) {
        layer.open({
            type: 2,
            title: '查看协议文档',
            content: '{:url("Store/status")}?id=' + depositId,
            area: ['80%', '70%'],
            btn: ['关闭'],
            yes: function(index) {
                layer.close(index);
            }
        });
    }
</script>