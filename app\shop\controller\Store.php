<?php


namespace app\shop\controller;


use app\common\basics\ShopBase;
use app\common\server\ConfigServer;
use app\common\server\JsonServer;
use app\shop\logic\StoreLogic;
use think\facade\Db;

class Store extends ShopBase
{
    /**
     * @Notes: 商家设置
     * @Author: 张无忌
     */
    public function index()
    {
        $label=ConfigServer::get('website_shop', 'merchant_tags');
        if($label){
            $label=explode('|', $label);
        }else{
            $label=[];
        }
        return view('', [
            'detail' => StoreLogic::detail($this->shop_id),
            'shop_label' => $label,
            'tx_map_key' => ConfigServer::get('map', 'tx_map_key')
        ]);
    }

    /**
     * @Notes: 编辑商家
     * @Author: 张无忌
     */
    public function edit()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $res = StoreLogic::edit($post);
            if ($res === false) {
                $error = StoreLogic::getError() ?: '更新失败';
                return JsonServer::error($error);
            }
            return JsonServer::success('更新成功');
        }
        return JsonServer::error('异常');
    }


    /*
     * 集采联盟
     */
    public function jclist(){
        $depositAmount=ConfigServer::get('shop_entry', 'procurementDeposit');
        return view('', [
            'detail' => StoreLogic::detail($this->shop_id),
            'depositAmount' => $depositAmount,
            'deposit' => Db::name('shop_deposit')->where('shop_id', $this->shop_id)->find(),
            'tx_map_key' => ConfigServer::get('map', 'tx_map_key')
        ]);
    }

    /**
     * @notes PC获取支付状态
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/10/29 3:52 下午
     */
    public function getPayStatus()
    {
        $id = $this->request->get('id');
        $pay_status=Db::name('shop_deposit')->where('id', $id)->value('pay_status');
        if($pay_status==1){
            $result['pay_status']='已支付';
            $result['status']='paid';
            return JsonServer::success('', $result);
        }else{
            return JsonServer::success('');
        }
        if ($pay_status !== false) {
            return JsonServer::success('', $result);
        } else {
            return JsonServer::error('参数错误');
        }
    }
    /**
     * @notes PC获取广告支付状态
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/10/29 3:52 下午
     */
    public function getAdPayStatus()
    {
        $id = $this->request->get('id');
        $pay_status=Db::name('ad_order')->where('id', $id)->value('status');
        if($pay_status==1){
            $result['pay_status']='已支付';
            $result['status']='paid';
            return JsonServer::success('', $result);
        }else{
            return JsonServer::success('');
        }
        if ($pay_status !== false) {
            return JsonServer::success('', $result);
        } else {
            return JsonServer::error('参数错误');
        }
    }
    public function lists(){
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $lists = StoreLogic::lists($post,$this->shop_id);
            if ($lists === false) {
                $message = StoreLogic::getError() ?: '提交失败';
                return JsonServer::error($message);
            }
            return JsonServer::success('提交成功',$lists);
        }
         $detail = StoreLogic::getDepositStatus(0, $this->shop_id);
         return view('', [
            'deposit' => $detail
        ]);
         
    }
    /**
     * @Notes: 上传资质缴纳入驻费
     * @Author: 张无忌
     */
    public function add()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();

            // 检查协议字段（兼容新旧字段名）
            if(!isset($post['agreementNew']) && !isset($post['agreement'])){
                return JsonServer::error('请查看协议并勾选同意协议选项!');
            }

            // 处理文件路径
            if(empty($post['filePaths']) || !is_array($post['filePaths'])){
                return JsonServer::error('请上传签署的合同!');
            }

            //去除$post['filePaths']为空的数组元素
            foreach ($post['filePaths'] as $k=>$v){
                if(empty($v)){
                    unset($post['filePaths'][$k]);
                }
            }

            if(empty($post['filePaths'])){
                return JsonServer::error('请上传签署的合同!');
            }

            $result = StoreLogic::saveAgree($post, $this->shop_id);
            if ($result === false) {
                $message = StoreLogic::getError() ?: '提交失败';
                return JsonServer::error($message);
            }

            // 返回成功信息，包含跳转提示
            return JsonServer::success('申请提交成功！请前往列表页面进行支付', [
                'redirect_url' => url('Store/jclist'),
                'need_payment' => true
            ]);
        }
        //返回当前网址域名带https
        $domain = request()->domain();
        
        return view('', [
            'domain' => $domain
        ]);
      

    }

    /**
     * @Notes: 查看申请状态
     * @Author: 张无忌
     */
    public function status()
    {
        $id = $this->request->get('id');
        if (empty($id)) {
            return JsonServer::error('参数错误');
        }

        $detail = StoreLogic::getDepositStatus($id, $this->shop_id);
        if (!$detail) {
            return JsonServer::error('记录不存在');
        }

        return view('', [
            'detail' => $detail
        ]);
    }

    /**
     * @Notes: 查看保证金明细
     * @Author: 张无忌
     */
    public function depositDetail()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $shop_id = $this->shop_id;
            $deposit_id = $get['deposit_id'] ?? 0;

            // 获取明细数据
            $where = ['shop_id' => $shop_id];
            if ($deposit_id > 0) {
                $where['deposit_id'] = $deposit_id;
            }

            $model = new \app\common\model\shop\ShopDepositDetails();
            $lists = $model->where($where)
                ->order('id', 'desc')
                ->paginate([
                    'page' => $get['page'] ?? 1,
                    'list_rows' => $get['limit'] ?? 15,
                    'var_page' => 'page'
                ])
                ->toArray();

            // 格式化数据
            foreach ($lists['data'] as &$item) {
                // 格式化变动类型
                $changeTypes = [
                    1 => '缴纳',
                    2 => '增加',
                    3 => '扣除',
                    4 => '退还'
                ];
                $item['change_type_text'] = $changeTypes[$item['change_type']] ?? '未知';

                // 格式化金额显示
                $item['deposit_change_text'] = ($item['deposit_change'] > 0 ? '+' : '') . $item['deposit_change'];

                // 格式化时间
                if (!empty($item['created_at'])) {
                    $item['created_at'] = date('Y-m-d H:i', strtotime($item['created_at']));
                }
                if (!empty($item['change_date'])) {
                    $item['change_date'] = date('Y-m-d', strtotime($item['change_date']));
                }
            }

            return JsonServer::success('获取成功', [
                'count' => $lists['total'],
                'lists' => $lists['data']
            ]);
        }

        $deposit_id = $this->request->get('deposit_id', 0);

        // 获取保证金基本信息
        $deposit = \think\facade\Db::name('shop_deposit')
            ->where('shop_id', $this->shop_id);

        if ($deposit_id > 0) {
            $deposit = $deposit->where('id', $deposit_id);
        }

        $deposit = $deposit->order('id', 'desc')->find();

        if (!$deposit) {
            return JsonServer::error('保证金记录不存在');
        }

        return view('', [
            'deposit' => $deposit,
            'shop_id' => $this->shop_id,
            'deposit_id' => $deposit_id
        ]);
    }
}