<?php
namespace app\api\logic;

use app\common\basics\Logic;
use app\common\model\goods\GoodsColumn;
use app\api\logic\GoodsLogic;
use \app\api\logic\JcaiLogic;
use app\common\model\goods\Goods;
class GoodsColumnLogic extends Logic
{
    /**
     * 获取商品栏目列表
     */
    public static function getGoodsColumnList()
    {
        $where = [
            'del' => 0, // 未删除
            'status' => 1, // 显示
        ];
        $list = GoodsColumn::field('id,name,remark,image')
            ->where($where)
            ->order('sort', 'asc')
            ->select()
            ->toArray();
        return $list;
    }


    public static function getGoodsColumnMatch()
    {
        //清空所有商品的标签
        Goods::where('id', '>', 0)->whereRaw('!FIND_IN_SET(9, goods_label_top)')->update(['goods_label_top'=>'','goods_label' => '', 'is_hot' => 0]);
        // 定义查询条件
        $where = [
            'del' => 0,
            'status' => 1,
        ];
        // 获取商品分类ID
        $columnIds = GoodsColumn::field('id')->where($where)->order('sort', 'asc')->column('id');

        // 定义商品列表和对应的标签处理函数
        $goodsLists = [
            ['list' => GoodsLogic::chooseByExpert(['page_no' => 1, 'page_size' => 20]), 'labelKey' => 'goods_label_top', 'labelValue' => 1],
            ['list' => GoodsLogic::getpdGoodsList(['page_no' => 1, 'page_size' => 20]), 'labelKey' => 'goods_label_top', 'labelValue' => 7],
//            ['list' => JcaiLogic::activity(['page_no' => 1, 'page_size' => 2000]), 'labelKey' => 'goods_label_top', 'labelValue' => 6],
            ['list' => GoodsLogic::getHotSellers(['page_no' => 1, 'page_size' => 20]), 'labelKey' => 'is_hot', 'labelValue' => 5],
            ['list' => GoodsLogic::getAnnualTopProducts(['page_no' => 1, 'page_size' => 20]), 'labelKey' => 'goods_label', 'labelValue' => 4],
            ['list' => GoodsLogic::getProductCollectionRanking(['page_no' => 1, 'page_size' => 20]), 'labelKey' => 'goods_label', 'labelValue' => 3],
            ['list' => GoodsLogic::getNewProduct(['page_no' => 1, 'page_size' => 20]), 'labelKey' => 'goods_label_top', 'labelValue' => 2],
            ['list' => GoodsLogic::getRecommendedPurchaseList(['page_no' => 1, 'page_size' => 20]), 'labelKey' => 'goods_label', 'labelValue' => 8],
        ];

        // 初始化数据数组
        $data = [
            'goods_label_top' => [],
            'is_hot' => [],
            'goods_label' => [],
        ];

        foreach ($goodsLists as $item) {
            $lists = $item['list']['lists'] ?? [];
            if (!empty($lists)) {
                foreach ($lists as $good) {
                    if (in_array($item['labelValue'], $columnIds)) {
                        // 检查当前商品是否已经存在于数据数组中
                        $goodId = $good['id'];
                        if (!isset($data[$item['labelKey']][$goodId])) {
                            // 如果不存在，则初始化标签数组
                            $data[$item['labelKey']][$goodId] = [];
                        }
                        // 将标签添加到对应商品的标签数组中
                        $data[$item['labelKey']][$goodId][] = $item['labelValue'];
                    }
                }
            }
        }

        // 准备更新数据
        $updates = [];
        foreach ($data as $key => $labels) {
            foreach ($labels as $goodId => $labelValues) {
                // 去重并用逗号分隔标签值
                $uniqueLabelValues = implode(',', array_unique($labelValues));
                // 将更新数据添加到更新数组中
                $updates[$goodId][$key] = $uniqueLabelValues;
            }
        }

        // 执行更新操作
        foreach ($updates as $id => $updateData) {
            Goods::update($updateData, ['id' => $id]);
        }

        return true;
    }


}