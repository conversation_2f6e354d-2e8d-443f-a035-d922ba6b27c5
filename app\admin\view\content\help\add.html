{layout name="layout2" /}
<link href="__PUBLIC__/static/lib/layui/layeditor/layedit.css" rel="stylesheet"/>
<script src="__PUBLIC__/static/lib/layui/layeditor/index.js"></script>
<script src="__PUBLIC__/static/lib/layui/layeditor/ace/ace.js"></script>
<style>
    .layui-form-item .layui-input-inline { width: 340px; }
</style>

<div class="layui-card layui-form" style="box-shadow:none;">
    <div class="layui-card-body">
        <div class="layui-form-item">
            <label for="title" class="layui-form-label"><span style="color:red;">*</span>帮助标题：</label>
            <div class="layui-input-inline">
                <input type="text" name="title" id="title" lay-verType="tips" lay-verify="required" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label for="cid" class="layui-form-label"><span style="color:red;">*</span>帮助分类：</label>
            <div class="layui-input-inline">
                <select name="cid" id="cid" lay-verType="tips" lay-verify="required">
                    <option value="">全部</option>
                    {volist name="category" id="vo"}
                        <option value="{$vo.id}">{$vo.name}</option>
                    {/volist}
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">标签：</label>
            <div class="layui-input-block">
                <input type="checkbox" name="cate_label[]" value="普通用户组协议/规则" title="普通用户组协议/规则">
                <input type="checkbox" name="cate_label[]" value="普通商家组协议/规则" title="普通商家组协议/规则">
                <input type="checkbox" name="cate_label[]" value="商家会员组协议/规则" title="商家会员组协议/规则">
                <input type="checkbox" name="cate_label[]" value="实力厂商组协议/规则" title="实力厂商组协议/规则">
                <input type="checkbox" name="cate_label[]" value="集采购商家协议/规则" title="集采购商家协议/规则">
                <input type="checkbox" name="cate_label[]" value="招商顾问组协议/规则" title="招商顾问组协议/规则">
                <input type="checkbox" name="cate_label[]" value="集采购用户协议/规则" title="集采购用户协议/规则">
            </div>
        </div>

        <div class="layui-form-item">
            <label for="intro" class="layui-form-label">帮助简介：</label>
            <div class="layui-input-inline">
                <input type="text" name="intro" id="intro" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label for="sort" class="layui-form-label">帮助排序：</label>
            <div class="layui-input-inline">
                <input type="text" name="sort" id="sort" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">封面图：</label>
            <div class="layui-input-block">
                <div class="like-upload-image" switch-tab="0" lay-verType="tips">
                    <div class="upload-image-elem"><a class="add-upload-image"> + 添加图片</a></div>
                </div>
                <div class="layui-form-mid layui-word-aux">建议尺寸：200*150像素</div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">文档附件：</label>
            <div class="layui-input-block">
                <div class="layui-upload">
                    <button type="button" class="layui-btn layui-btn-normal" id="upload-document">选择文档</button>
                    <div class="layui-upload-list">
                        <div id="document-list"></div>
                    </div>
                    <input type="hidden" name="document_path" id="document_path" value="">
                    <input type="hidden" name="document_name" id="document_name" value="">
                </div>
                <div class="layui-form-mid layui-word-aux">支持格式：pdf、doc、docx、xls、xlsx、ppt、pptx等</div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">帮助状态：</label>
            <div class="layui-input-inline">
                <input type="radio" name="is_show" value="1" title="显示">
                <input type="radio" name="is_show" value="0" title="隐藏" checked>
            </div>
        </div>
        <div class="layui-form-item">
            <label for="content" class="layui-form-label">帮助内容：</label>
            <div class="layui-input-block">
                <textarea name="content" id="content" lay-verify="content"></textarea>
            </div>
        </div>

        <div class="layui-form-item layui-hide">
            <input type="button" lay-submit lay-filter="addSubmit" id="addSubmit" value="确认">
        </div>
    </div>
</div>

<script>
    layui.config({
        base: "/static/lib/"
    }).extend({
        likeedit: "likeedit/likeedit"
    }).use(["layEditor", "form", "upload"], function(){
        var form = layui.form;
        var upload = layui.upload;
        var layEditor = layui.layEditor;
        layEditor.set({
            uploadImage: {
                url: '{:url("file/lists")}?type=10'
            },
        })
        var ieditor = layEditor.build('content')
        form.verify({
            content: function(value) {
                return layEditor.sync(ieditor);
            }
        });

        like.delUpload();
        $(document).on("click", ".add-upload-image", function () {
            like.imageUpload({
                limit: 1,
                field: "image",
                that: $(this)
            });
        });

        // 文档上传
        upload.render({
            elem: '#upload-document',
            url: '{:url("Upload/document")}',
            accept: 'file',
            exts: 'pdf|doc|docx|xls|xlsx|ppt|pptx|txt|zip|rar',
            size: 10240, // 10MB
            done: function(res){
                if(res.code === 1) {
                    $('#document_path').val(res.data.uri);
                    $('#document_name').val(res.data.name);
                    $('#document-list').html('<p style="color: #5FB878;"><i class="layui-icon layui-icon-file"></i> ' + res.data.name + ' <a href="javascript:;" onclick="removeDocument()" style="color: #FF5722; margin-left: 10px;">[删除]</a></p>');
                    layer.msg('文档上传成功');
                } else {
                    layer.msg(res.msg || '上传失败');
                }
            },
            error: function(){
                layer.msg('上传失败');
            }
        });
    })

    // 删除文档
    function removeDocument() {
        $('#document_path').val('');
        $('#document_name').val('');
        $('#document-list').html('');
        layer.msg('文档已删除');
    }
</script>