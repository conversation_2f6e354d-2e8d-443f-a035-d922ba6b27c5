<?php
/**
 * 优化后的搜索推荐算法 - 简化版实现
 * 模仿淘宝推荐逻辑，注重实用性和效果
 */

class OptimizedSearchLogic
{
    // 词性权重配置 - 基于ls_word表的tags字段
    private static $tagWeights = [
        // 高价值词汇
        '产品-品牌' => 30,
        '产品类型-简单' => 25,
        '产品类型属性词' => 22,
        '促销词' => 20,
        '产品类型修饰词' => 18,
        '产品-型号' => 16,
        
        // 中价值词汇
        '专有名词-中药材' => 15,
        '专有名词-药品' => 15,
        '专有名词-动物' => 14,
        '专有名词-植物' => 14,
        '产品类型-统称' => 12,
        
        // 低价值词汇
        '基本词-中文' => 5,
        '基本词-中文词组' => 5,
        
        // 负价值词汇
        '色情词汇-中文' => -10,
        '仿品词类' => -10,
        '非正品词类' => -10,
    ];

    /**
     * 获取搜索推荐词汇 - 主入口
     */
    public static function getRecommendations($userId = null)
    {
        try {
            $recommendations = [];
            
            // 1. 个性化推荐（已登录用户）
            if ($userId) {
                $personalWords = self::getPersonalizedWords($userId);
                $recommendations = array_merge($recommendations, $personalWords);
            }
            
            // 2. 热门推荐
            $hotWords = self::getHotWords();
            $recommendations = array_merge($recommendations, $hotWords);
            
            // 3. 智能推荐（基于商品标签）
            $smartWords = self::getSmartWords();
            $recommendations = array_merge($recommendations, $smartWords);
            
            // 4. 处理和过滤
            $finalWords = self::processWords($recommendations);
            
            return array_slice($finalWords, 0, 12);
            
        } catch (Exception $e) {
            error_log('搜索推荐算法错误: ' . $e->getMessage());
            return self::getFallbackWords();
        }
    }

    /**
     * 获取个性化推荐词汇
     */
    private static function getPersonalizedWords($userId)
    {
        // 模拟数据库查询用户搜索历史
        $userHistory = [
            ['keyword' => '鞋子', 'count' => 27],
            ['keyword' => '背心', 'count' => 3],
            ['keyword' => '森之露', 'count' => 1],
        ];
        
        $personalWords = [];
        
        foreach ($userHistory as $record) {
            $keyword = $record['keyword'];
            
            // 基于用户搜索历史生成相关推荐
            if ($keyword == '鞋子') {
                $personalWords = array_merge($personalWords, [
                    '运动鞋', '休闲鞋', '皮鞋', '帆布鞋', '高跟鞋'
                ]);
            } elseif ($keyword == '背心') {
                $personalWords = array_merge($personalWords, [
                    '吊带背心', '运动背心', '纯棉背心', '女士背心'
                ]);
            }
        }
        
        return array_unique($personalWords);
    }

    /**
     * 获取热门推荐词汇
     */
    private static function getHotWords()
    {
        // 模拟热门搜索词（基于搜索频次）
        $hotSearches = [
            '鞋子', '保健品', '面', '米', '背心', '益生菌', 
            '尿不湿', '适老化', '洗洁净'
        ];
        
        $hotWords = [];
        
        foreach ($hotSearches as $hotWord) {
            // 为热门词汇生成相关推荐
            if ($hotWord == '保健品') {
                $hotWords = array_merge($hotWords, ['维生素', '钙片', '蛋白粉']);
            } elseif ($hotWord == '益生菌') {
                $hotWords = array_merge($hotWords, ['肠道健康', '儿童益生菌', '成人益生菌']);
            }
        }
        
        return array_unique($hotWords);
    }

    /**
     * 获取智能推荐词汇 - 基于商品词汇标签
     */
    private static function getSmartWords()
    {
        // 模拟从ls_word表获取高价值词汇
        $highValueWords = [
            ['word' => '苹果', 'tags' => '产品-品牌'],
            ['word' => '手机', 'tags' => '产品类型-简单'],
            ['word' => '智能', 'tags' => '产品类型属性词'],
            ['word' => '华为', 'tags' => '产品-品牌'],
            ['word' => '耳机', 'tags' => '产品类型-简单'],
        ];
        
        $smartWords = [];
        
        foreach ($highValueWords as $wordInfo) {
            $weight = self::calculateWordWeight($wordInfo['tags']);
            
            if ($weight >= 15) { // 权重阈值
                $smartWords[] = $wordInfo['word'];
                
                // 生成组合词汇
                if ($wordInfo['word'] == '苹果') {
                    $smartWords[] = '苹果手机';
                } elseif ($wordInfo['word'] == '华为') {
                    $smartWords[] = '华为手机';
                }
            }
        }
        
        return array_unique($smartWords);
    }

    /**
     * 处理推荐词汇 - 去重、过滤、排序
     */
    private static function processWords($words)
    {
        // 1. 去重
        $words = array_unique($words);
        
        // 2. 过滤无效词汇
        $validWords = [];
        foreach ($words as $word) {
            if (self::isValidWord($word)) {
                $validWords[] = $word;
            }
        }
        
        // 3. 按质量排序
        usort($validWords, function($a, $b) {
            return self::getWordScore($b) <=> self::getWordScore($a);
        });
        
        return $validWords;
    }

    /**
     * 计算词汇权重
     */
    private static function calculateWordWeight($tags)
    {
        $weight = 0;
        $tagList = explode(',', $tags);
        
        foreach ($tagList as $tag) {
            $tag = trim($tag);
            if (isset(self::$tagWeights[$tag])) {
                $weight += self::$tagWeights[$tag];
            }
        }
        
        return $weight;
    }

    /**
     * 验证词汇是否有效
     */
    private static function isValidWord($word)
    {
        if (empty($word)) return false;
        
        $length = mb_strlen($word, 'UTF-8');
        
        // 长度检查：2-8个字符
        if ($length < 2 || $length > 8) return false;
        
        // 过滤纯数字
        if (is_numeric($word)) return false;
        
        // 过滤无意义词汇
        $meaningless = ['的', '和', '与', '或', '等', '了', '个'];
        if (in_array($word, $meaningless)) return false;
        
        return true;
    }

    /**
     * 获取词汇质量分数
     */
    private static function getWordScore($word)
    {
        $score = mb_strlen($word, 'UTF-8') * 2;
        
        // 商品相关词汇加分
        if (preg_match('/(手机|电脑|鞋子|服装|化妆品)/u', $word)) {
            $score += 10;
        }
        
        // 品牌词汇加分
        if (preg_match('/(苹果|华为|小米|耐克)/u', $word)) {
            $score += 15;
        }
        
        return $score;
    }

    /**
     * 获取备用推荐词汇
     */
    private static function getFallbackWords()
    {
        return [
            '手机', '电脑', '服装', '鞋子', '包包', '化妆品',
            '零食', '家电', '数码', '运动', '美食', '护肤品'
        ];
    }
}

// 测试新算法
echo "=== 测试优化后的搜索推荐算法 ===\n\n";

// 测试已登录用户
echo "1. 用户1130的个性化推荐：\n";
$userRecommendations = OptimizedSearchLogic::getRecommendations(1130);
foreach ($userRecommendations as $word) {
    echo "- {$word}\n";
}

echo "\n2. 未登录用户的热门推荐：\n";
$guestRecommendations = OptimizedSearchLogic::getRecommendations();
foreach ($guestRecommendations as $word) {
    echo "- {$word}\n";
}

echo "\n=== 算法特点 ===\n";
echo "✓ 基于用户搜索历史的个性化推荐\n";
echo "✓ 结合全站热门数据的智能推荐\n";
echo "✓ 利用词汇标签权重的质量控制\n";
echo "✓ 生成语义完整的推荐词汇\n";
echo "✓ 简洁高效的算法实现\n";