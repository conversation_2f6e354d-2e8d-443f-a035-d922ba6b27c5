<?php

namespace app\api\logic;

use app\common\basics\Logic;
use app\common\model\shop\ShopApply;
use app\common\model\shop\ShopMerchantfees;
use app\common\model\shop\Shop;
use app\common\model\user\User;
use app\common\server\ConfigServer;
use app\common\enum\PayEnum;
use app\common\enum\ShopTierEnum;
use app\admin\logic\shop\ApplyLogic;
use PSpell\Config;
use think\facade\Db;
use app\common\server\UrlServer;
use app\api\controller\Notification; // 引入Notification控制器

/**
 * 商家入驻逻辑
 * Class ShopEntryLogic
 * @package app\api\logic
 */
class ShopEntryLogic extends Logic
{
    /**
     * 获取入驻选项配置
     * @return array
     */
    public static function getEntryOptions($type)
    {
       
          
       if($type ==0){
        $ads = Db::name('ad')
            ->where('pid', 79)
            ->where('status', 1)
            ->select()
            ->toArray();
       }else{
        $ads = Db::name('ad')
            ->where('pid', 79)
            ->select()  
            ->toArray();
       }
     

        $entryOptions = [
            'no_vip' => ['name' => '0元入驻', 'price' => 0, 'data' => []],
            'vip' => ['name' => '商家会员', 'price' => ConfigServer::get('shop_entry', 'entry_fee', 0), 'data' => []],
            'svip' => ['name' => '实力厂商', 'price' => ConfigServer::get('shop_entry', 'zins_fee', 0), 'data' => []],
        ];

        $activeGroups = [];

        foreach ($ads as $ad) {
            $key = '';
            if (strpos($ad['title'], '0元入驻') !== false) {
                $key = 'no_vip';
            } elseif (strpos($ad['title'], '商家会员') !== false) {
                $key = 'vip';
            } elseif (strpos($ad['title'], '实力厂商') !== false) {
                $key = 'svip';
            }

            if ($key) {
            
                    $activeGroups[$key] = true;
                    if (!empty($ad['remark'])) {
                        $entryOptions[$key]['data'][$ad['remark']] = UrlServer::getFileUrl($ad['image']);
                    }
              
            }
        }

        $result = [];
        foreach ($entryOptions as $key => $value) {
            if (isset($activeGroups[$key])) {
                if($key=='no_vip'){
                    $doc=[];
                }else if($key=='vip'){
                    $doc=Db::name('help')->field('title,id')->where('id',5)->find();
                }else{
                    $doc=Db::name('help')->field('title,id')->where('id',10)->find();
                }
                $result[] = [
                    'name' => $value['name'],
                    'price' => $value['price'],
                    'doc' => $doc,
                   
                ] + $value['data'];
            }
        }
        
        $data = $result;

        if ($type) {
            $data = array_filter($data, function($item) {
                return $item['name'] !== '0元入驻';
            });
            $data = array_values($data);
        }

        return $data;
    }

    /**
     * 0元入驻
     * @param int $user_id
     * @param array $post
     * @return array|false
     */
    public static function freeEntry($user_id, $post)
    {
        Db::startTrans();
        try {
            // 检查是否已经入驻
            $existingApply = ShopApply::where('user_id', $user_id)
                ->where('del', 0)
                ->where('audit_status', '<>', 3)
                ->find();
            if ($existingApply) {
                throw new \Exception('您已提交过入驻申请');
            }

            // 验证商家名称及账号是否已存在
            self::validateShopInfo($post);

            // 创建申请记录
            $apply = ShopApply::create([
                'user_id' => $user_id,
                'cid' => $post['cid'],
                'name' => $post['name'],
                'nickname' => $post['nickname'],
                'mobile' => $post['mobile'],
                'account' => $post['account'],
                'shop_doc' => $post['shop_doc']??'',
                'password' => $post['password'],
                'license' => implode(',', $post['license']),
                'del' => 0,
                'audit_status' => 1, //待审核
            
            ]);
            
            // 直接创建商家
            // $shop_id = self::createShop($user_id, $apply, 0); // tier_level = 0 (0元入驻)
            // Db::name('user')->where('id', $user_id)->update(['shop_id' => $shop_id]);
            Db::commit();
            return [
                'shop_id' => 0,
                'tier_level' => 0,
                'message' => '请等待平台审核!'
            ];
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 创建商家会员支付订单
     * @param int $user_id
     * @param array $post
     * @return array|false
     */
    public static function createMemberPayOrder($user_id, $post)
    {
        try {
            // 检查是否已有未支付订单
            $existingOrder = ShopMerchantfees::where('user_id', $user_id)
                ->where('status', PayEnum::UNPAID)
                ->where('feetype', 0) // 商家会员
                ->find();
            if ($existingOrder) {
                return [
                    'id' => $existingOrder->id,
                    'order_sn' => $existingOrder->order_sn,
                    'amount' => $existingOrder->amount
                ];
            }

            $money = ConfigServer::get('shop_entry', 'entry_fee', 0);
            $order_data = [
                'user_id' => $user_id,
                'shop_id' => 0,
                'order_sn' => createSn('shop_merchantfees', 'order_sn'),
                'amount' => $money,
                'feetype' => 0, // 商家会员
                'status' => PayEnum::UNPAID,
                'created_at' => date('Y-m-d H:i:s'),
                'tier_level' => 1, // 商家会员等级
                'tier_type' => ShopTierEnum::TYPE_NEW_ENTRY // 新入驻
            ];

            $id = Db::name('shop_merchantfees')->insertGetId($order_data);
            if ($id) {
                return [
                    'id' => $id,
                    'order_sn' => $order_data['order_sn'],
                    'amount' => $money
                ];
            }
            return false;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 创建实力厂商支付订单
     * @param int $user_id
     * @param array $post
     * @return array|false
     */
    public static function createPremiumPayOrder($user_id, $post)
    {
        try {
            // 检查是否已有未支付订单
            $existingOrder = ShopMerchantfees::where('user_id', $user_id)
                ->where('status', PayEnum::UNPAID)
                ->where('feetype', 1) // 实力厂商
                ->find();
            if ($existingOrder) {
                return [
                    'id' => $existingOrder->id,
                    'order_sn' => $existingOrder->order_sn,
                    'amount' => $existingOrder->amount
                ];
            }

            $money = ConfigServer::get('shop_entry', 'zins_fee', 0);
            $order_data = [
                'user_id' => $user_id,
                'shop_id' => 0,
                'order_sn' => createSn('shop_merchantfees', 'order_sn'),
                'amount' => $money,
                'feetype' => 1, // 实力厂商
                'status' => PayEnum::UNPAID,
                'created_at' => date('Y-m-d H:i:s'),
                'tier_level' => 2, // 实力厂商等级
                'tier_type' => ShopTierEnum::TYPE_NEW_ENTRY // 新入驻
            ];

            $id = Db::name('shop_merchantfees')->insertGetId($order_data);
            if ($id) {
                return [
                    'id' => $id,
                    'order_sn' => $order_data['order_sn'],
                    'amount' => $money
                ];
            }
            return false;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 商家会员入驻申请
     * @param int $user_id
     * @param array $post
     * @return array|false
     */
    public static function memberApply($user_id, $post)
    {
        Db::startTrans();
        try {
            // 检查是否已支付
            $paidOrder = ShopMerchantfees::where('user_id', $user_id)
                ->where('status', PayEnum::ISPAID)
                ->where('feetype', 0)
                ->where('tier_type', ShopTierEnum::TYPE_NEW_ENTRY)
                ->find();
            if (!$paidOrder) {
                throw new \Exception('请先完成商家会员支付');
            }

            // 检查是否已经申请过
            $existingApply = ShopApply::where('user_id', $user_id)
                ->where('del', 0)
                ->find();
            if ($existingApply) {
                ShopApply::where('user_id', $user_id)
                ->where('del', 0)
                ->delete();
              $apply = ShopApply::create([
                'user_id' => $user_id,
                'cid' => $post['cid'],
                'name' => $post['name'],
                'nickname' => $post['nickname'],
                'mobile' => $post['mobile'],
                'account' => $post['account'],
                'shop_doc' => $post['shop_doc'],
                'password' => $post['password'],
                'license' => implode(',', $post['license']),
                'del' => 0,
                'audit_status' => 1, // 直接通过
                'audit_explain' => '商家会员已支付',
                'apply_time' => time()
                ]);
              
            }else{
  // 验证商家信息
            self::validateShopInfo($post);

            // 创建申请记录
            $apply = ShopApply::create([
                'user_id' => $user_id,
                'cid' => $post['cid'],
                'name' => $post['name'],
                'nickname' => $post['nickname'],
                'mobile' => $post['mobile'],
                'account' => $post['account'],
                  'shop_doc' => $post['shop_doc'],
                'password' => $post['password'],
                'license' => implode(',', $post['license']),
                'del' => 0,
                'audit_status' => 1, // 直接通过
                'audit_explain' => '商家会员已支付',
                'apply_time' => time()
            ]);

                // 创建商家
                $shop_id = self::createShop($user_id, $apply, 1); // tier_level = 1 (商家会员)

                // 更新支付订单的shop_id
                $paidOrder->shop_id = $shop_id;
                $paidOrder->save();

            }
            Db::commit();
            return [
                'tier_level' => 1,
                'message' => '商家会员入驻成功'
            ];
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 实力厂商入驻申请
     * @param int $user_id
     * @param array $post
     * @return array|false
     */
    public static function premiumApply($user_id, $post)
    {
        Db::startTrans();
        try {
            // 检查是否已支付
            $paidOrder = ShopMerchantfees::where('user_id', $user_id)
                ->where('status', PayEnum::ISPAID)
                ->where('feetype', 1)
                ->where('tier_type', ShopTierEnum::TYPE_NEW_ENTRY)
                ->find();
            if (!$paidOrder) {
                throw new \Exception('请先完成实力厂商支付');
            }

            // 检查是否已经申请过
            $existingApply = ShopApply::where('user_id', $user_id)
                ->where('del', 0)
                ->find();
            if ($existingApply) {
                throw new \Exception('您已提交过入驻申请');
            }

            // 验证商家信息
            self::validateShopInfo($post);

            // 创建申请记录
            $apply = ShopApply::create([
                'user_id' => $user_id,
                'cid' => $post['cid'],
                'name' => $post['name'],
                'nickname' => $post['nickname'],
                'mobile' => $post['mobile'],
                'account' => $post['account'],
                'password' => $post['password'],
                'shop_doc' => $post['shop_doc'],
                'license' => implode(',', $post['license']),
                'del' => 0,
                'audit_status' => 2, // 直接通过
                'audit_explain' => '实力厂商已支付，自动通过',
                'apply_time' => time()
            ]);

            // 创建商家
            $shop_id = self::createShop($user_id, $apply, 2); // tier_level = 2 (实力厂商)

            // 更新支付订单的shop_id
            $paidOrder->shop_id = $shop_id;
            $paidOrder->save();

            Db::commit();
            return [
                'shop_id' => $shop_id,
                'tier_level' => 2,
                'message' => '实力厂商入驻成功'
            ];
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 记录用户点击0元入驻按钮
     * @param int $user_id
     * @return bool
     */
    public static function recordFreeEntryClick($user_id)
    {
        try {
            $user = User::find($user_id);
            if (!$user) {
                self::$error = '用户不存在';
                return false;
            }

            // 更新用户的0元入驻点击状态
            $user->free_entry_clicked = $user->free_entry_clicked ? 0 : 1;
            $user->save();

            return true;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 获取用户入驻状态
     * @param int $user_id
     * @return array
     */
    public static function getEntryStatus($user_id)
    {

        // 检查是否已经是商家
        $user = User::find($user_id);
          $doc_path=  Db::name('help')->where('id',34)->value('document_path');
           $doc_path=UrlServer::getFileUrl($doc_path);
        // 检查是否有已支付但未填写信息的订单
        $paidOrders = ShopMerchantfees::where('user_id', $user_id)
            ->where('status', PayEnum::ISPAID)
            ->where('tier_type', ShopTierEnum::TYPE_NEW_ENTRY)
            ->select();
        if ($paidOrders->count() > 0) {
            $order = $paidOrders[0];
            $user->free_entry_clicked = 1;
        }
        return [
            // 'status' => 'paid_pending_info',
            'free_entry_clicked' => $user->free_entry_clicked,
            'tier_level' => $order->tier_level ?? 0,
            'message' => '尚未开始入驻',
              'doc'=>  $doc_path
        ];
        if ($user && $user->shop_id > 0) {
            $shop = Shop::find($user->shop_id);
            return [
                'shop_id' => $user->shop_id,
                'tier_level' => $shop->tier_level ?? 0,
                'tier_name' => self::getTierName($shop->tier_level ?? 0),
                'free_entry_clicked' => 1,
                'doc'=>  $doc_path,
                'message' => '已入驻成功'
            ];
        }

        // 检查是否有已支付但未填写信息的订单
        $paidOrders = ShopMerchantfees::where('user_id', $user_id)
            ->where('status', PayEnum::ISPAID)
            ->where('tier_type', ShopTierEnum::TYPE_NEW_ENTRY)
            ->select();

        if ($paidOrders->count() > 0) {
            $order = $paidOrders[0];
            return [

                'feetype' => $order->feetype,
                'tier_level' => $order->tier_level,
                'tier_name' => self::getTierName($order->tier_level),
                'amount' => $order->amount,
                  'doc'=>  $doc_path,
                'payment_date' => $order->payment_date,
                'free_entry_clicked' => 1,
                'message' => '已支付，请填写入驻信息'
            ];
        }

        // 检查是否有未支付订单
        $unpaidOrders = ShopMerchantfees::where('user_id', $user_id)
            ->where('status', PayEnum::UNPAID)
            ->select();

        if ($unpaidOrders->count() > 0) {
            $order = $unpaidOrders[0];
            return [
                'order_sn' => $order->order_sn,
                'feetype' => $order->feetype,
                'tier_level' => $order->tier_level,
                'tier_name' => self::getTierName($order->tier_level),
                'amount' => $order->amount,
                  'doc'=>  $doc_path,
                'free_entry_clicked' => 0,
                'message' => '有未支付订单'
            ];
        }

        return [
            // 'status' => 'paid_pending_info',
            'free_entry_clicked' => $user->free_entry_clicked,
            'tier_level' => 0,
              'doc'=>  $doc_path,
            'message' => '尚未开始入驻'
        ];
    }

    /**
     * 验证商家信息
     * @param array $post
     * @throws \Exception
     */
    private static function validateShopInfo($post)
    {
        // 验证商家名称是否已存在
        $nameExists = ShopApply::where('del', 0)
            ->where('audit_status', '<>', 3)
            ->where('name', $post['name'])
            ->find();
        if ($nameExists) {
            throw new \Exception('商家名称已存在');
        }

        // 验证商家账号是否已存在
        $accountExists = ShopApply::where('del', 0)
            ->where('audit_status', '<>', 3)
            ->where('account', $post['account'])
            ->find();
        if ($accountExists) {
            throw new \Exception('商家账号已存在');
        }
    }

    /**
     * 创建商家
     * @param int $user_id
     * @param ShopApply $apply
     * @param int $tier_level
     * @return int
     */
    private static function createShop($user_id, $apply, $tier_level)
    {
        // 使用现有的审核逻辑创建商家
        $post = [
            'id' => $apply->id,
            'audit_status' => 1,
            'audit_explain' => $apply->audit_explain
        ];
        ApplyLogic::audit($post);

        // 获取创建的商家ID
        $user = User::find($user_id);
        $shop_id = $user->shop_id;

        // 更新商家等级
        if ($shop_id > 0) {
            $shop_data = [
                'tier_level' => $tier_level,
                'is_run' => 0
            ];

            if ($tier_level == 1) {
                // 商家会员
                $shop_data['entry_time'] = time() + (365 * 24 * 3600); // 1年后到期
                $shop_data['expire_time'] = time() + (365 * 24 * 3600);
            } elseif ($tier_level == 2) {
                // 实力厂商
                $shop_data['yan_fee'] = 1;
                $shop_data['entry_time'] = time() + (365 * 24 * 3600);
                $shop_data['expire_time'] = time() + (365 * 24 * 3600);
                $shop_data['yan_time'] = time() + (365 * 24 * 3600);
            }

            Db::name('shop')->where('id', $shop_id)->update($shop_data);
        }

        return $shop_id;
    }

    /**
     * 获取等级名称
     * @param int $tier_level
     * @return string
     */
    private static function getTierName($tier_level)
    {
        $names = [
            0 => '0元入驻',
            1 => '商家会员',
            2 => '实力厂商'
        ];
        return $names[$tier_level] ?? '未知等级';
    }
}
