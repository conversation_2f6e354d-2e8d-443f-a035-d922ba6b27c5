<?php


namespace app\api\logic;


use app\common\basics\Logic;
use app\common\model\content\Help;
use app\common\model\content\HelpCategory;
use app\common\server\JsonServer;
use app\common\server\UrlServer;
use think\facade\Db;

class HelpLogic extends Logic
{
    public static function category()
    {
        $where = [
            'del' => 0,
            'is_show' => 1
        ];
        $data = HelpCategory::field('id,name')->where($where)->select()->toArray();
        return $data;
    }

    public static function lists($get)
    {
     
        //根据用户ID查询身份,返回对应身份的协议列表
        //协议标签如下集采购用户协议/规则
        // 招商顾问组协议/规则
        //集采购商家协议/规则
        // 普通商家组协议/规则
        //普通用户组协议/规则
        //都能看的协议
        //集采购会员身份是ls_user表的jcvip是否是集采会员0否1是
        //普通商家身份是ls_user表的shop_id是否是普通商家不等于0就是商家用户
        //招商顾问身份是ls_user表的is_agent是否是招商顾问 0不是1是
        //普通用户身份是ls_user表存在就是普通用户
        //集采购商家身份是ls_shop关联ls_user表中的shop_id,ls_shop中jcshop_vip,大于0的就是集采购商家

        // 基础条件
        $where = [
            ['h.del', '=', 0],
            ['h.is_show', '=', 1],
            ['c.del', '=', 0],
            ['c.is_show', '=', 1],
        ];

        // 如果前端传入了指定分类ID，则直接按分类ID筛选
        if(isset($get['cid']) && !empty($get['cid'])) {
            $where[] = ['h.cid', '=', $get['cid']];
        }
       
        if($get['cid']=='5'){
    // 否则根据用户身份筛选对应的协议
       if(isset($get['user_id']) && !empty($get['user_id'])) {
            $user_id = $get['user_id'];

            // 查询用户信息
            $user = Db::name('user')->where('id', $user_id)->find();

            // 用户可见的标签数组
            $visible_labels = [];

            // 所有用户都是普通用户，可以看到普通用户组协议/规则
            $visible_labels[] = '普通用户组协议/规则';

            // 判断是否是集采购会员
            if($user && isset($user['jcvip']) && $user['jcvip'] == 1) {
                $visible_labels[] = '集采购用户协议/规则';
            }

            // 判断是否是招商顾问
            if($user && isset($user['is_agent']) && $user['is_agent'] == 1) {
                $visible_labels[] = '招商顾问组协议/规则';
            }

            // 判断是否是商家用户
            if($user && isset($user['shop_id']) && $user['shop_id'] > 0) {
                // 查询商家信息，判断是否是集采购商家
                $shop = Db::name('shop')->where('id', $user['shop_id'])->find();

                if($shop && isset($shop['jcshop_vip']) && $shop['jcshop_vip'] > 0) {
                    $visible_labels[] = '集采购商家协议/规则';
                } else {
                   if($shop){
                       if($shop['tier_level']==0){
                            $visible_labels[] = '普通商家组协议/规则';
                       }else if($shop['tier_level']==1){
                            $visible_labels[] = '商家会员组协议/规则';
                       }else if($shop['tier_level']==2){
                            $visible_labels[] = '实力厂商组协议/规则';
                       }
                   
                   }
                }
            }

            // 构建标签查询条件
            $labelWhere = [];
            foreach ($visible_labels as $label) {
                $labelWhere[] = ['h.cate_label', 'like', '%'.$label.'%'];
            }

            // 将标签条件以OR方式连接
            if (!empty($labelWhere)) {
                $whereOr = [];
                foreach ($labelWhere as $condition) {
                    $whereOr[] = $condition;
                }
                // 使用whereOr方法添加条件
                $where[] = function ($query) use ($whereOr) {
                    foreach ($whereOr as $condition) {
                        $query->whereOr([$condition]);
                    }
                };
            }
       }
        }
    
           

        $order = [
            'sort' => 'asc',
            'id' => 'desc'
        ];

        $model = new Help();

        $list = $model->alias('h')
            ->join('help_category c', 'c.id = h.cid')
            ->field(['h.id', 'h.title','h.content', 'h.intro', 'h.image', 'h.visit', 'h.likes', 'h.create_time', 'h.cate_label', 'c.name as category_name'])
            ->where($where)
            ->order($order)
            ->page($get['page_no'], $get['page_size'])
            ->select()
            ->toArray();
          
        // 处理图片路径
        foreach($list as &$item) {
            if(!empty($item['image'])) {
                $item['image'] = UrlServer::getFileUrl($item['image']);
            }
            if(!empty($item['document_path'])) {
                $item['document_path'] = UrlServer::getFileUrl($item['document_path']);
            }
        }

        $count = $model->alias('h')->join('help_category c', 'c.id = h.cid')->where($where)->count();

        $more = is_more($count, $get['page_no'], $get['page_size']);

        $data = [
            'list' => $list,
            'count' => $count,
            'more' => $more
        ];
        return $data;
    }

    public static function detail($id)
    {
        $help =  Help::field('id,title,create_time,visit,content,cate_label')->where('id', $id)->findOrEmpty();

        if($help->isEmpty()) {
            $help = [];
        }else{
            $help->visit = $help->visit + 1;
            $help->save();
            $help = $help->toArray();
        }

        $recommend_list = Db::name('help')
            ->where([['del','=','0'], ['id','<>',$id]])
            ->field('id,title,image,visit')
            ->order('visit desc')
            ->limit(5)
            ->select()
            ->toArray();

        foreach ($recommend_list as $key => $recommend){
            $recommend_list[$key]['image'] = empty($recommend['image']) ? "" : UrlServer::getFileUrl($recommend['image']);
             if(!empty($recommend['document_path'])) {
                $recommend_list[$key]['document_path'] = UrlServer::getFileUrl($recommend_list[$key]['document_path']);
            }
        }

        $help['recommend_list'] = $recommend_list;
        return $help;
    }


    /*
     *
     * 添加反馈信息
     */
    public static function addFeedback($user_id,$post){
        try{
            //将数组图片转换成用逗号分隔的字符串
            if(isset($post['images']) && !empty($post['images'])){
                $post['images'] = implode(',',$post['images']);
            }
            $data = [
                'user_id'=>$user_id,
                'images'=>$post['images'],
                'content'=>$post['content']
            ];
            Db::name('feedback')->insert($data);
            return JsonServer::success('添加成功');
        }catch(\Exception $e){
            return JsonServer::error(1002,[$e->getMessage()]);
        }
    }
}